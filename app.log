2025-05-05 18:47:39,736 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-05-05 18:47:40,095 - Worker - INFO - Em<PERSON> using >> nancy_ol9s8_4@gmx.<NAME_EMAIL>
2025-05-05 18:48:10,994 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-05-05 18:48:11,354 - Worker - INFO - Email <PERSON> using >> <EMAIL> to y<PERSON><PERSON><PERSON><PERSON>@hotmail.com
2025-05-07 00:06:39,006 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 00:06:39,012 - VPNManager - INFO - Turning on VPN...
2025-05-07 00:06:39,012 - VP<PERSON><PERSON>anager - DEBUG - HMA VPN executable found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 00:06:39,012 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 1/3)
2025-05-07 00:06:39,064 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:06:39,065 - VPNManager - WARNING - Failed to turn on VPN (attempt 1/3)
2025-05-07 00:06:39,065 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:06:44,080 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 2/3)
2025-05-07 00:06:44,126 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:06:44,127 - VPNManager - WARNING - Failed to turn on VPN (attempt 2/3)
2025-05-07 00:06:44,127 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:06:49,130 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 3/3)
2025-05-07 00:06:49,179 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:06:49,179 - VPNManager - WARNING - Failed to turn on VPN (attempt 3/3)
2025-05-07 00:06:49,180 - VPNManager - ERROR - Failed to turn on VPN after multiple attempts
2025-05-07 00:11:08,190 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 00:11:08,194 - VPNManager - INFO - Turning on VPN...
2025-05-07 00:11:08,194 - VPNManager - DEBUG - HMA VPN executable found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 00:11:08,194 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 1/3)
2025-05-07 00:11:08,242 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:11:08,243 - VPNManager - WARNING - Failed to turn on VPN (attempt 1/3)
2025-05-07 00:11:08,243 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:11:13,253 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 2/3)
2025-05-07 00:11:13,304 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:11:13,304 - VPNManager - WARNING - Failed to turn on VPN (attempt 2/3)
2025-05-07 00:11:13,304 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:11:18,310 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 3/3)
2025-05-07 00:11:18,355 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:11:18,355 - VPNManager - WARNING - Failed to turn on VPN (attempt 3/3)
2025-05-07 00:11:18,355 - VPNManager - ERROR - Failed to turn on VPN after multiple attempts
2025-05-07 00:11:30,623 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-05-07 00:12:18,058 - App - INFO - Application closing - cleaning up threads
2025-05-07 00:12:18,058 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-05-07 00:21:26,280 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 00:21:26,284 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:26,308 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:26,415 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:26,416 - VPNManager - INFO - VPN is already connected according to network check
2025-05-07 00:21:26,416 - VPNManager - DEBUG - Getting current public IP address...
2025-05-07 00:21:27,383 - VPNManager - DEBUG - Current IP address: **************
2025-05-07 00:21:36,664 - VPNManager - DEBUG - Getting current public IP address...
2025-05-07 00:21:36,963 - VPNManager - DEBUG - Current IP address: **************
2025-05-07 00:21:36,963 - VPNManager - DEBUG - ensure_vpn_off called, current state: vpn_enabled=True
2025-05-07 00:21:36,963 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:36,986 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:37,061 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:37,061 - VPNManager - INFO - VPN is still connected, attempting to disconnect...
2025-05-07 00:21:37,061 - VPNManager - DEBUG - HMA VPN executable found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 00:21:37,061 - VPNManager - DEBUG - Attempting to turn off VPN (attempt 1/3)
2025-05-07 00:21:37,369 - VPNManager - DEBUG - run_hma_command('turn_off') returned: True
2025-05-07 00:21:37,369 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:37,391 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:37,466 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:37,467 - VPNManager - WARNING - Failed to turn off VPN (attempt 1/3)
2025-05-07 00:21:37,467 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:21:42,481 - VPNManager - DEBUG - Attempting to turn off VPN (attempt 2/3)
2025-05-07 00:21:42,790 - VPNManager - DEBUG - run_hma_command('turn_off') returned: True
2025-05-07 00:21:42,790 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:42,812 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:42,923 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:42,923 - VPNManager - WARNING - Failed to turn off VPN (attempt 2/3)
2025-05-07 00:21:42,923 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:21:47,924 - VPNManager - DEBUG - Attempting to turn off VPN (attempt 3/3)
2025-05-07 00:21:48,232 - VPNManager - DEBUG - run_hma_command('turn_off') returned: True
2025-05-07 00:21:48,232 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:48,256 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:48,361 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:48,361 - VPNManager - WARNING - Failed to turn off VPN (attempt 3/3)
2025-05-07 00:21:48,361 - VPNManager - ERROR - Failed to turn off VPN after multiple attempts
2025-05-07 00:27:21,928 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-05-07 00:28:42,516 - App - INFO - Application closing - cleaning up threads
2025-05-07 00:28:42,516 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-05-07 17:12:41,950 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:13:20,365 - App - WARNING - Health check thread did not terminate within timeout
2025-05-07 17:16:45,910 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:16:57,545 - App - WARNING - Health check thread did not terminate within timeout
2025-05-07 17:23:46,058 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:29:04,054 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 17:29:04,054 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 17:29:04,054 - SimpleVPN - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:29:04,129 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 17:29:04,129 - SimpleVPN - INFO - VPN is already connected
2025-05-07 17:29:04,129 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:29:04,523 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:29:09,256 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:29:09,536 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:29:09,536 - SimpleVPN - DEBUG - ensure_vpn_off called, current state: vpn_enabled=True
2025-05-07 17:29:09,604 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 17:34:46,391 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 17:34:46,391 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 17:34:46,393 - SimpleVPN - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:34:46,470 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 17:34:46,470 - SimpleVPN - INFO - VPN is already connected
2025-05-07 17:34:46,470 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:34:46,812 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:34:50,297 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:34:50,584 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:34:50,584 - SimpleVPN - DEBUG - ensure_vpn_off called, current state: vpn_enabled=True
2025-05-07 17:34:50,652 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 17:34:53,510 - App - WARNING - Health check thread did not terminate within timeout
2025-05-07 17:43:34,503 - App - INFO - pywinauto is installed
2025-05-07 17:43:37,729 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 17:43:37,729 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 17:44:03,023 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-05-07 17:44:04,891 - App - INFO - Application closing - cleaning up threads
2025-05-07 17:44:04,891 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-05-07 17:47:52,287 - App - INFO - pywinauto is installed
2025-05-07 17:47:54,581 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 17:47:54,581 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 17:48:03,800 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:48:04,147 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:48:16,364 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:48:16,943 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 17:48:36,770 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:48:37,143 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:48:51,828 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:48:52,392 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 17:49:11,856 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:49:12,206 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:49:26,668 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:49:27,230 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 17:49:46,588 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:49:46,947 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:50:01,319 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:50:01,769 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 17:50:03,472 - App - INFO - Application closing - cleaning up threads
2025-05-07 17:50:03,472 - App - INFO - Stopping health check thread
2025-05-07 20:38:02,242 - App - INFO - pywinauto is installed
2025-05-07 20:38:06,275 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 20:38:06,283 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 20:38:15,982 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:38:16,447 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:38:26,186 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:38:26,783 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:38:46,594 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:38:46,985 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:39:04,079 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:39:04,762 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:39:24,107 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:39:24,484 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:39:38,802 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:39:39,245 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:39:58,702 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:39:59,072 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:40:18,309 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:40:18,915 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:40:38,269 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:40:38,905 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:41:02,746 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:41:03,196 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:41:22,371 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:41:22,726 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:41:39,747 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:41:40,340 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:41:59,603 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:42:00,128 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:42:18,555 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:42:19,156 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:42:38,577 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:42:38,932 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:42:53,857 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:42:54,453 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:43:13,826 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:43:14,203 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:43:29,357 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:43:30,442 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:43:50,032 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:43:50,426 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:44:06,323 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:44:06,895 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:44:26,192 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:44:26,667 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:44:43,107 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:44:44,106 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:45:03,638 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:45:04,028 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:45:22,364 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:45:22,979 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:45:42,288 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:45:42,650 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:45:57,853 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:45:58,318 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:46:17,647 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:46:18,020 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:46:58,540 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:46:59,132 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:47:18,824 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:47:19,192 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:47:34,273 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:47:34,860 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:47:54,341 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:47:54,719 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:49:08,559 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:49:09,117 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:49:28,430 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:49:28,865 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:49:45,649 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:49:46,213 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:50:05,689 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:50:06,082 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:50:20,424 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:50:20,892 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:50:40,321 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:50:40,661 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:52:42,713 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:52:43,317 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:53:02,638 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:53:03,047 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:53:42,751 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:53:43,377 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:54:02,625 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:54:02,970 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:54:07,759 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:54:08,318 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:54:19,093 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:54:19,482 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 21:03:14,701 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-05-07 21:11:13,435 - App - INFO - Application closing - cleaning up threads
2025-05-07 21:11:13,435 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-05-07 21:11:13,435 - App - ERROR - Error cleaning up export thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 22:19:49,545 - App - INFO - pywinauto is installed
2025-06-14 22:19:56,405 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:19:56,405 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:20:02,663 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:20:03,014 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 22:20:03,667 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:20:03,667 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:20:03,670 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:20:04,603 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:20:04,604 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:20:04,604 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:20:04,605 - urllib3.connectionpool - DEBUG - Resetting dropped connection: login.microsoftonline.com
2025-06-14 22:20:05,620 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:20:06,077 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:22:02,255 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 0a859f62-6085-47be-8d87-0392bac5fe66
2025-06-14 22:22:03,165 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-14 22:22:03,165 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "GAQABIQEAAABVrSpeuWamRam2jAF1XRQEDJG37_yTIgkZVO4Wyq1DTv3UnA7xsEHRlV2jPoHVMpRc54zhs2vlh8AT4HMpNAZWBCD-Ybbjl6rXWhbxILN8zWyroqD8ZH6VTNhR2JrJ_4uJE2aBJOckaf9URzvMJ7Q3IvVrQo4ZGy6V4Bd2jbn2r6rcyo9RQwAZ5uJKwc9s9tAg7e9UyXeXFnuvKZfwIVIIhKKH04wnXxszWfYWzrVTFRPS9ykvy9BPa3K0-ISW0GItqIoqLWLOaZmJKX0TPnyTKjH8sld-44I1zdk9f29dj1wvSZRhZ-Z7IVLZ8zYQwQLK5fNZoc_Hlj-2Xb9PizN1GWNcH-4l5ai0jfj3_jmvOiAA",
        "device_code": "GAQABIQEAAABVrSpeuWamRam2jAF1XRQEDJG37_yTIgkZVO4Wyq1DTv3UnA7xsEHRlV2jPoHVMpRc54zhs2vlh8AT4HMpNAZWBCD-Ybbjl6rXWhbxILN8zWyroqD8ZH6VTNhR2JrJ_4uJE2aBJOckaf9URzvMJ7Q3IvVrQo4ZGy6V4Bd2jbn2r6rcyo9RQwAZ5uJKwc9s9tAg7e9UyXeXFnuvKZfwIVIIhKKH04wnXxszWfYWzrVTFRPS9ykvy9BPa3K0-ISW0GItqIoqLWLOaZmJKX0TPnyTKjH8sld-44I1zdk9f29dj1wvSZRhZ-Z7IVLZ8zYQwQLK5fNZoc_Hlj-2Xb9PizN1GWNcH-4l5ai0jfj3_jmvOiAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-14 22:22:03,180 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:22:03,700 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-14 22:22:12,198 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:22:12,616 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 22:29:56,974 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 22:29:57,923 - App - INFO - Application closing - cleaning up threads
2025-06-14 22:29:57,923 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 22:30:05,677 - App - INFO - pywinauto is installed
2025-06-14 22:30:08,644 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:30:08,644 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:30:14,013 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:30:14,444 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:30:15,114 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:30:15,114 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:30:15,114 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:30:15,620 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:30:15,620 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:30:15,624 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:30:15,745 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:30:16,222 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:34:51,363 - App - INFO - pywinauto is installed
2025-06-14 22:34:54,233 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:34:54,233 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:34:59,643 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:35:00,083 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:35:00,753 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:35:00,753 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:35:00,758 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:35:01,198 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:35:01,199 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:35:01,199 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:35:01,290 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:35:01,689 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:47:36,813 - App - INFO - pywinauto is installed
2025-06-14 22:47:46,599 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:47:46,600 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:47:52,020 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:47:52,443 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:47:53,121 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:47:53,121 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:47:53,124 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:47:53,557 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:47:53,557 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:47:53,557 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:47:53,660 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:47:54,378 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:50:06,269 - App - INFO - pywinauto is installed
2025-06-14 22:50:08,948 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:50:08,948 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:50:14,558 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:50:14,908 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:50:15,588 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:50:15,593 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:50:15,595 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:50:16,054 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:50:16,055 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:50:16,055 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:50:16,143 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:50:16,495 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:51:56,816 - App - INFO - pywinauto is installed
2025-06-14 22:52:00,015 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:52:00,016 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:52:05,484 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:52:05,915 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:52:06,583 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:52:06,583 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:52:06,587 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:52:06,947 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:52:06,947 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:52:06,947 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:52:07,053 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:52:07,976 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:59:56,343 - App - INFO - pywinauto is installed
2025-06-14 22:59:59,453 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:59:59,453 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:00:04,903 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:00:05,327 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:00:05,998 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:00:05,998 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:00:06,001 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:00:06,436 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:00:06,437 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:00:06,437 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:00:06,535 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:00:06,905 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:02:27,203 - App - INFO - pywinauto is installed
2025-06-14 23:02:32,156 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:02:32,156 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:02:37,554 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:02:37,893 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:02:38,563 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:02:38,563 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:02:38,563 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:02:38,953 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:02:38,956 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:02:38,956 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:02:39,069 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:02:39,613 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:06:38,302 - App - INFO - pywinauto is installed
2025-06-14 23:08:36,938 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:08:36,938 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:08:42,380 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:08:42,814 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:08:43,480 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:08:43,480 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:08:43,482 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:08:43,920 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:08:43,921 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:08:43,921 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:08:44,013 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:08:44,467 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:22:24,944 - App - INFO - pywinauto is installed
2025-06-14 23:22:27,664 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:22:27,664 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:22:33,189 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:22:33,630 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:22:34,303 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:22:34,303 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:22:34,307 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:22:34,806 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:22:34,807 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:22:34,807 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:22:34,927 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:22:35,468 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:22:35,469 - oauth2_dialog - INFO - === OAUTH2 DIALOG CREATION STARTED ===
2025-06-14 23:22:35,469 - oauth2_dialog - INFO - Dialog thread ID: 28396
2025-06-14 23:22:35,469 - oauth2_dialog - INFO - Dialog thread name: Dummy-1
2025-06-14 23:22:35,469 - oauth2_dialog - INFO - User code: X2LDZZUJ
2025-06-14 23:22:35,469 - oauth2_dialog - INFO - Verification URI: https://www.microsoft.com/link
2025-06-14 23:22:35,471 - oauth2_dialog - INFO - PyQt6 imports successful
2025-06-14 23:35:47,501 - App - INFO - pywinauto is installed
2025-06-14 23:35:59,221 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:35:59,221 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:36:04,624 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:36:05,041 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:36:05,716 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:36:05,716 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:36:05,718 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:36:06,234 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:36:06,235 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:36:06,235 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:36:06,355 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:36:06,891 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:40:19,589 - App - INFO - pywinauto is installed
2025-06-14 23:40:36,243 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:40:36,243 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:40:41,753 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:40:42,184 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:40:42,865 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:40:42,865 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:40:42,869 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:40:43,302 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:40:43,303 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:40:43,303 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:40:43,409 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:40:43,797 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:40:50,193 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:40:50,539 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:40:56,124 - App - WARNING - Health check thread did not terminate within timeout
2025-06-14 23:41:03,107 - App - INFO - pywinauto is installed
2025-06-14 23:41:12,953 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:41:12,955 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:41:18,471 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:41:18,829 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:41:19,505 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:41:19,505 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:41:19,509 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:41:19,886 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:41:19,886 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:41:19,886 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:41:20,006 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:41:20,356 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:41:26,875 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:41:27,222 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:41:35,466 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:41:35,843 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 23:48:34,320 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 23:48:35,811 - App - INFO - Application closing - cleaning up threads
2025-06-14 23:48:35,811 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 23:48:39,769 - App - INFO - pywinauto is installed
2025-06-14 23:48:43,409 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:48:43,409 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:48:49,717 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:48:50,072 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 23:48:50,672 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:48:50,672 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:48:50,676 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:48:51,041 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:48:51,042 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:48:51,043 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:48:51,137 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:48:51,532 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:54:37,348 - App - INFO - pywinauto is installed
2025-06-14 23:54:40,489 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:54:40,489 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:54:46,005 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:54:46,429 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:54:47,140 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:54:47,140 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:54:47,140 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:54:47,579 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:54:47,579 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:54:47,579 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:54:47,690 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:54:48,109 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:56:10,384 - App - INFO - pywinauto is installed
2025-06-14 23:56:14,361 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:56:14,361 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:56:19,794 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:56:20,145 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:56:20,819 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:56:20,819 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:56:20,819 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:56:21,185 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:56:21,185 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:56:21,185 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:56:21,301 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:56:21,747 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:57:26,710 - App - INFO - pywinauto is installed
2025-06-14 23:57:30,039 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:57:30,039 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:57:35,616 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:57:35,972 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:57:36,649 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:57:36,649 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:57:36,649 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:57:37,089 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:57:37,089 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:57:37,089 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:57:37,199 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:57:37,630 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:03:59,462 - App - INFO - pywinauto is installed
2025-06-15 00:04:25,335 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:04:25,335 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:04:30,718 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:04:32,208 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 00:04:33,325 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:04:33,325 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:04:33,325 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:04:33,742 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:04:33,742 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:04:33,742 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:04:33,842 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:04:34,359 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:05:05,722 - App - INFO - pywinauto is installed
2025-06-15 00:05:09,299 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:05:09,299 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:05:14,659 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:05:15,009 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 00:05:15,654 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:05:15,654 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:05:15,670 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:05:16,014 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:05:16,014 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:05:16,014 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:05:16,134 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:05:16,603 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:13:38,681 - App - INFO - pywinauto is installed
2025-06-15 00:14:49,101 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:14:49,101 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:14:54,542 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:14:54,969 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 00:14:55,620 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:14:55,620 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:14:55,629 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:14:56,121 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:14:56,121 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:14:56,121 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:14:56,249 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:14:56,810 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:15:53,558 - App - INFO - pywinauto is installed
2025-06-15 00:15:56,639 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:15:56,639 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:16:02,055 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:16:02,406 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 00:16:03,085 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:16:03,085 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:16:03,088 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:16:03,504 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:16:03,505 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:16:03,505 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:16:03,630 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:16:04,083 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:16:27,220 - App - INFO - pywinauto is installed
2025-06-15 00:16:29,787 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:16:29,787 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:16:35,184 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:16:35,527 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 00:16:36,198 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:16:36,198 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:16:36,198 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:16:36,610 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:16:36,611 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:16:36,611 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:16:36,729 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:16:37,627 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:22:54,229 - App - INFO - pywinauto is installed
2025-06-15 00:23:06,564 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:23:06,564 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:23:11,970 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:23:12,410 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 00:23:13,054 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:23:13,054 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:23:13,057 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:23:13,550 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:23:13,550 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:23:13,550 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:23:13,690 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:23:14,238 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:23:20,504 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:23:20,844 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 00:23:29,113 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:23:31,300 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 00:25:28,665 - App - INFO - pywinauto is installed
2025-06-15 00:33:34,849 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:33:34,849 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:33:41,056 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:33:41,439 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 00:33:42,239 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:33:42,239 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:33:42,245 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:33:43,069 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:33:43,070 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:33:43,070 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:33:43,174 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:33:43,669 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:34:52,044 - msal.telemetry - DEBUG - Generate or reuse correlation_id: f9fb3fb9-cf02-4019-9ffd-708ef6b4e2fa
2025-06-15 00:34:53,431 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 00:34:53,432 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "DAQABIQEAAABVrSpeuWamRam2jAF1XRQEiYcgbqGAO-mS1L30BjnR_9HE2RLwEexPFypLvI7tQ7OcEjz818gmI9_cLsfoH-IoavJrWY7zLdIVajFsrYnAyscbCjPWlnV4Whn23r29X4JhS_877k3gfO6xXngQhHNJ73Z7kyUlFebRRqBHQKUUZygcZk5kdTl61rt8jU-dxfnVipoFZkgmh4NEuEcmhPDZp67cy6PbEZaQmtqP7RR_3xcpU4t1X0b-MnPTOo-f1U-dtrXDZ_1weEvPzS15YqeqhiNiR_3em71GAZRwqnbNBdYHzCozPT_VQHQ8TA3ZaLyXVwCc7bTzeJM_ggyVDVq7Zl9InZwyCqbWpn4zNB3r4CAA",
        "device_code": "DAQABIQEAAABVrSpeuWamRam2jAF1XRQEiYcgbqGAO-mS1L30BjnR_9HE2RLwEexPFypLvI7tQ7OcEjz818gmI9_cLsfoH-IoavJrWY7zLdIVajFsrYnAyscbCjPWlnV4Whn23r29X4JhS_877k3gfO6xXngQhHNJ73Z7kyUlFebRRqBHQKUUZygcZk5kdTl61rt8jU-dxfnVipoFZkgmh4NEuEcmhPDZp67cy6PbEZaQmtqP7RR_3xcpU4t1X0b-MnPTOo-f1U-dtrXDZ_1weEvPzS15YqeqhiNiR_3em71GAZRwqnbNBdYHzCozPT_VQHQ8TA3ZaLyXVwCc7bTzeJM_ggyVDVq7Zl9InZwyCqbWpn4zNB3r4CAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 00:34:53,447 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:34:53,864 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 00:35:02,118 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:35:02,505 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 00:36:28,954 - App - INFO - pywinauto is installed
2025-06-15 00:36:35,516 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:36:35,516 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:36:41,674 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:36:41,989 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 00:36:42,483 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:36:42,483 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:36:42,486 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:36:42,949 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:36:42,950 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:36:42,950 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:36:43,353 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:36:43,897 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:37:06,260 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 38032324-c4d1-440c-81bb-f654bacac3a6
2025-06-15 00:37:06,262 - urllib3.connectionpool - DEBUG - Resetting dropped connection: login.microsoftonline.com
2025-06-15 00:37:07,808 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:13,660 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:19,574 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:24,930 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:30,376 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:35,762 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:41,250 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:46,688 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:52,948 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:37:58,363 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:38:03,887 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:38:09,390 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:38:14,853 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:38:20,283 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:38:25,710 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:38:31,197 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 00:38:40,414 - App - INFO - pywinauto is installed
2025-06-15 00:38:46,018 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 00:38:46,018 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 00:38:51,520 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:38:51,960 - SimpleVPN - DEBUG - Current IP address: **********
2025-06-15 00:38:52,634 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:38:52,634 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:38:52,637 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:38:53,156 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:38:53,157 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:38:53,157 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:38:53,277 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:38:53,683 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:39:19,297 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 0d1bac26-d1fe-4e75-98a2-eb819461a54c
2025-06-15 00:39:20,039 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 00:39:20,040 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "CAQABIQEAAABVrSpeuWamRam2jAF1XRQEqE57Z9dyZ1LtXPnikT3UIIo4L5RV7CeBYmOKfW0pKkqre6JDRziZz8IRsKoRcRAJH1cXJxuxRjyGU_U5pJU28Lo0nkCDEWyO0zW-6qO_G4Jj_09keHwMDoFv1yULqbthfi1YZmbcLk3-xE7v9aCFZt8dSXAhLJuicDsc-dAZ9mG5F39Zz6aIZ45Jldx1dItLQvDmWaheGI-POByGzXsv1Wp5TZ2HCugUM4zf0FA64RYuLwhlluZ5jKYDRsVGgq53e3yXTC10NVmgMOvv_TgHD7VCom7hxWZpYa4aoCgUNBG01PCps-_8NmWDy_1PGmlqYA-j9ivrnL9McRyKSg3LkyAA",
        "device_code": "CAQABIQEAAABVrSpeuWamRam2jAF1XRQEqE57Z9dyZ1LtXPnikT3UIIo4L5RV7CeBYmOKfW0pKkqre6JDRziZz8IRsKoRcRAJH1cXJxuxRjyGU_U5pJU28Lo0nkCDEWyO0zW-6qO_G4Jj_09keHwMDoFv1yULqbthfi1YZmbcLk3-xE7v9aCFZt8dSXAhLJuicDsc-dAZ9mG5F39Zz6aIZ45Jldx1dItLQvDmWaheGI-POByGzXsv1Wp5TZ2HCugUM4zf0FA64RYuLwhlluZ5jKYDRsVGgq53e3yXTC10NVmgMOvv_TgHD7VCom7hxWZpYa4aoCgUNBG01PCps-_8NmWDy_1PGmlqYA-j9ivrnL9McRyKSg3LkyAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 00:39:25,233 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:39:25,582 - SimpleVPN - DEBUG - Current IP address: **********
2025-06-15 00:39:34,051 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:39:34,425 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 00:39:49,099 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:39:49,391 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 00:39:49,792 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:39:49,793 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:39:49,794 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:39:50,557 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:39:50,557 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:39:50,557 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:39:50,805 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:39:50,808 - urllib3.connectionpool - DEBUG - Resetting dropped connection: login.microsoftonline.com
2025-06-15 00:39:51,480 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:40:21,887 - msal.telemetry - DEBUG - Generate or reuse correlation_id: ec921d48-e468-4cb1-baa7-649f499018ff
2025-06-15 00:40:22,961 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 00:40:22,962 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "DAQABIQEAAABVrSpeuWamRam2jAF1XRQEPHwxUQgH7nQaLXh6CN9s4QtwJmr4RN7EqCLxyFUK1av6yjIf55ectiX8mvU4qwjW3F57nrSJbQ1fDuzEG8zBqKOth0yIEr1wBZ6YmdEkSnNmtRx00MMAucksyhwIamcud-cKX7lYRDGxKQkcgd1z8oqW2vKApUVxJiZMfL_nZ9T-At4XhDYAdJF5231sM5f-lX5Ho3kL-s7-GLsZJJtac9J1YKFZpdD4ZZgkKDg0UjyQYph4thqBgqn03Vdj_ZUt0hu9d-u_4YdIjPXinJOzJsTxwe31xB4mL-5xs1UbKtS2yqcubjuiIiGnNdDZuL__zwHFLW3V3YFCxzPS3JHAqSAA",
        "device_code": "DAQABIQEAAABVrSpeuWamRam2jAF1XRQEPHwxUQgH7nQaLXh6CN9s4QtwJmr4RN7EqCLxyFUK1av6yjIf55ectiX8mvU4qwjW3F57nrSJbQ1fDuzEG8zBqKOth0yIEr1wBZ6YmdEkSnNmtRx00MMAucksyhwIamcud-cKX7lYRDGxKQkcgd1z8oqW2vKApUVxJiZMfL_nZ9T-At4XhDYAdJF5231sM5f-lX5Ho3kL-s7-GLsZJJtac9J1YKFZpdD4ZZgkKDg0UjyQYph4thqBgqn03Vdj_ZUt0hu9d-u_4YdIjPXinJOzJsTxwe31xB4mL-5xs1UbKtS2yqcubjuiIiGnNdDZuL__zwHFLW3V3YFCxzPS3JHAqSAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 00:40:22,967 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:40:23,408 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 00:40:31,658 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 00:40:32,032 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 00:40:35,935 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:40:35,935 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:40:35,936 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:40:36,445 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:40:36,446 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:40:36,446 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:40:36,587 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:40:37,051 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:41:00,854 - msal.telemetry - DEBUG - Generate or reuse correlation_id: e8b49461-22fc-45e5-9a20-0a9061e39865
2025-06-15 00:41:01,816 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 00:41:01,816 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "GAQABIQEAAABVrSpeuWamRam2jAF1XRQEuhBYVTymPBpjBTMMrtPbTrT7Yr_aoAzCXdFdk_57yzxeLm556Dn9NTQdFTo7QaChrpy24J1fke40REGOmxOANTqvdTm5M6xqMp6E0NnL3tD4sawHo7IwY5vykEDvueCuDaxv3VBqer74kM42z6O0kod50eLTWOwmLvdeDI9fjXRy93DtCqFguwpRlD86WuBVICRgqtebpNm0BdBcnxVYkQTiGXj2Yy9HAT9Mk5SWTDxj32Jqp4YoXUEM8GyzFkWmlmQomZhc6dqu86lnSplHAHmJT7jq9bkCsgLNjoqrpzW9iI1jkZ8YLjBmYN8boqak0_KSgHYOa-EGtAbtquQb2CAA",
        "device_code": "GAQABIQEAAABVrSpeuWamRam2jAF1XRQEuhBYVTymPBpjBTMMrtPbTrT7Yr_aoAzCXdFdk_57yzxeLm556Dn9NTQdFTo7QaChrpy24J1fke40REGOmxOANTqvdTm5M6xqMp6E0NnL3tD4sawHo7IwY5vykEDvueCuDaxv3VBqer74kM42z6O0kod50eLTWOwmLvdeDI9fjXRy93DtCqFguwpRlD86WuBVICRgqtebpNm0BdBcnxVYkQTiGXj2Yy9HAT9Mk5SWTDxj32Jqp4YoXUEM8GyzFkWmlmQomZhc6dqu86lnSplHAHmJT7jq9bkCsgLNjoqrpzW9iI1jkZ8YLjBmYN8boqak0_KSgHYOa-EGtAbtquQb2CAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 00:42:26,873 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:42:26,873 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:42:26,879 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:42:27,334 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:42:27,334 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:42:27,334 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:42:27,474 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:42:27,974 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:49:06,909 - App - INFO - pywinauto is installed
2025-06-15 00:49:15,677 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:49:15,677 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:49:15,684 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:49:17,529 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:49:17,530 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:49:17,530 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:49:17,742 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 00:49:18,134 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 00:50:09,767 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 93228dbd-1a58-49f5-aa19-84bc0bed2405
2025-06-15 00:50:10,888 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 00:50:10,889 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "DAQABIQEAAABVrSpeuWamRam2jAF1XRQEYn3DZbk6L02-LCAAqPQ9GW_F51uLKD0rBccYIJR5kz95VIdctZVyyyIY8uZaSRUOtSZtVSA4YF1d_VIiiNHDDFRwSsOQBUps1NO-ouncYoyc2pzO40bG1k9aYSZsDvMh6FJ93csYuACdIhQQVQDTeH_DbRXK34C3pw9GUJbpqLFX_O03HbBLreUE77XYGvsl68JgljkkQ_B8Gyybg1w-3ThxyKzzAvF4pU2oLNk57b76bCH0P3Tvze4kU_5CFMDJYze2eSmyyvcB5WThlqpWNqLruNVyMyZ-kAT3HDoWAXDNyGRubV7QcUxvUsr3j4kIvcPVW6l73MBWdMfyTjLh9yAA",
        "device_code": "DAQABIQEAAABVrSpeuWamRam2jAF1XRQEYn3DZbk6L02-LCAAqPQ9GW_F51uLKD0rBccYIJR5kz95VIdctZVyyyIY8uZaSRUOtSZtVSA4YF1d_VIiiNHDDFRwSsOQBUps1NO-ouncYoyc2pzO40bG1k9aYSZsDvMh6FJ93csYuACdIhQQVQDTeH_DbRXK34C3pw9GUJbpqLFX_O03HbBLreUE77XYGvsl68JgljkkQ_B8Gyybg1w-3ThxyKzzAvF4pU2oLNk57b76bCH0P3Tvze4kU_5CFMDJYze2eSmyyvcB5WThlqpWNqLruNVyMyZ-kAT3HDoWAXDNyGRubV7QcUxvUsr3j4kIvcPVW6l73MBWdMfyTjLh9yAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 00:50:37,433 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:50:37,433 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:50:37,433 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:50:37,853 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:50:37,853 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:50:37,853 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:50:37,853 - msal.application - DEBUG - Cache hit an AT
2025-06-15 00:50:37,853 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 33d25770-4900-4e47-beac-2050d64c0a10
2025-06-15 00:50:47,555 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-15 00:51:03,803 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:51:03,803 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:51:03,807 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:51:04,243 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:51:04,243 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:51:04,243 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:51:04,253 - msal.application - DEBUG - Cache hit an AT
2025-06-15 00:51:04,253 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 9af04159-dc2c-49ee-986d-2cdb3d410046
2025-06-15 00:51:09,343 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-15 00:51:14,465 - Worker - ERROR - Error Email Not Sent >> <EMAIL> (530, b'5.7.57 Client not authenticated to send mail. [PR1P264CA0122.FRAP264.PROD.OUTLOOK.COM 2025-06-14T23:51:14.284Z 08DDAB9354330224]', '<EMAIL>')
2025-06-15 00:51:48,402 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 00:51:48,402 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 00:51:48,402 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 00:51:48,842 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 00:51:48,842 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 00:51:48,842 - msal.application - DEBUG - Broker enabled? None
2025-06-15 00:51:48,842 - msal.application - DEBUG - Cache hit an AT
2025-06-15 00:51:48,842 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 311db8c5-2f6f-4536-b003-f23667be790b
2025-06-15 00:51:53,946 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-15 00:51:59,087 - Worker - ERROR - Error Email Not Sent >> <EMAIL> (530, b'5.7.57 Client not authenticated to send mail. [PR2P264CA0041.FRAP264.PROD.OUTLOOK.COM 2025-06-14T23:51:58.900Z 08DDAB624CFBC948]', '<EMAIL>')
2025-06-15 00:59:26,475 - App - INFO - pywinauto is installed
2025-06-15 01:00:55,577 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 01:00:55,578 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 01:01:01,797 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 01:01:02,134 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 01:01:02,626 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:01:02,626 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 01:01:02,630 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:01:03,117 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 01:01:03,118 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:01:03,118 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:01:03,119 - msal.application - DEBUG - Found 1 RTs matching {'environment': 'login.microsoftonline.com', 'home_account_id': '********.9188040d-6c67-4c5b-b112-36a304b66dad', 'client_id': '9868b800-1d91-4573-9cf9-c2061096c3cb'}
2025-06-15 01:01:03,120 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 6a0797c0-6c67-4968-837f-16f331deb799
2025-06-15 01:01:03,121 - msal.application - DEBUG - Cache attempts an RT
2025-06-15 01:01:03,424 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 01:01:03,425 - msal.application - DEBUG - Refresh failed. invalid_scope: AADSTS70011: The provided resource value for the input parameter 'scope' is not valid. Trace ID: 909c0d9c-99e0-4d4b-8e37-39d5ca8c4000 Correlation ID: 6a0797c0-6c67-4968-837f-16f331deb799 Timestamp: 2025-06-15 00:01:03Z
2025-06-15 01:01:03,566 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 01:01:03,880 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 01:01:04,792 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-15 01:01:13,042 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 01:01:13,399 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 01:01:19,634 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:01:19,634 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 01:01:19,635 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:01:20,116 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 01:01:20,117 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:01:20,117 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:01:20,118 - msal.application - DEBUG - Found 1 RTs matching {'environment': 'login.microsoftonline.com', 'home_account_id': '********.9188040d-6c67-4c5b-b112-36a304b66dad', 'client_id': '9868b800-1d91-4573-9cf9-c2061096c3cb'}
2025-06-15 01:01:20,118 - msal.telemetry - DEBUG - Generate or reuse correlation_id: ce28657b-a821-4b4d-b0ab-a70671239cdd
2025-06-15 01:01:20,118 - msal.application - DEBUG - Cache attempts an RT
2025-06-15 01:01:20,514 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 01:01:20,515 - msal.application - DEBUG - Refresh failed. invalid_scope: AADSTS70011: The provided resource value for the input parameter 'scope' is not valid. Trace ID: 740266b4-46eb-4800-98aa-40ef162f4400 Correlation ID: ce28657b-a821-4b4d-b0ab-a70671239cdd Timestamp: 2025-06-15 00:01:20Z
2025-06-15 01:01:20,644 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 01:01:20,980 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 01:01:57,354 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-15 01:02:05,731 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 01:02:06,018 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 01:02:06,523 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:02:06,523 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 01:02:06,525 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:02:06,987 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 01:02:06,989 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:02:06,989 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:02:07,149 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 01:02:07,153 - urllib3.connectionpool - DEBUG - Resetting dropped connection: login.microsoftonline.com
2025-06-15 01:02:08,233 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 01:02:08,247 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 01:02:08,848 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-15 01:02:15,883 - App - WARNING - Health check thread did not terminate within timeout
2025-06-15 01:02:40,352 - App - INFO - pywinauto is installed
2025-06-15 01:02:45,571 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:02:45,571 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 01:02:45,571 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:02:46,339 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 01:02:46,340 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:02:46,340 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:02:46,541 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 01:02:46,971 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 01:08:35,576 - App - INFO - pywinauto is installed
2025-06-15 01:08:47,705 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:08:47,705 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/common
2025-06-15 01:08:47,707 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:08:48,238 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1547
2025-06-15 01:08:48,239 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/common/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/{tenantid}/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/common/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:08:48,239 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:08:48,345 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 01:08:48,601 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /common/oauth2/v2.0/devicecode HTTP/1.1" 400 537
2025-06-15 01:10:21,623 - App - INFO - pywinauto is installed
2025-06-15 01:10:30,970 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-15 01:10:30,970 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-15 01:10:37,167 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 01:10:37,515 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 01:10:38,672 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 01:10:39,264 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-15 01:10:46,671 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-15 01:10:47,028 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-15 01:10:49,188 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:10:49,188 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 01:10:49,192 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:10:49,685 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 01:10:49,686 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:10:49,686 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:10:49,842 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 01:10:50,332 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 01:12:59,434 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 7a95f50d-dad4-4554-a5d9-3e4292ee23b5
2025-06-15 01:12:59,437 - urllib3.connectionpool - DEBUG - Resetting dropped connection: login.microsoftonline.com
2025-06-15 01:13:00,615 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 01:13:00,615 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "CAQABIQEAAABVrSpeuWamRam2jAF1XRQEemvYslAUJF1V0uj3oksmrvLi7E8oQqs_R6KKG8-Thh823pUX-G5OlqOZHCbmvyy8Uc5ZdYeCYueh4B-WBbuTJmkmS__n7lOfXVK2gl4ECxYxyc-dHEzNKlPgo7gNhP-UXFyrYkMwwZBP9Qpu6T7AWh8S0gb2dH1CKEQm_mDaSdueItNlWVjdjHbpcrZO1tzoiUMixMavG6DlUiY6CxE-C4syJEN1fU3cysIrsK8cgRw0nPDs2F6rGWUsA6Yj5zYlWWibn_v6I5Tzz6ixikEMfDWh5pN4inLb9qJ-BOVwsSp1iRRB2_6zmpezqP_CqvHCbHmVs2f40XhUWcbIg8mzxSAA",
        "device_code": "CAQABIQEAAABVrSpeuWamRam2jAF1XRQEemvYslAUJF1V0uj3oksmrvLi7E8oQqs_R6KKG8-Thh823pUX-G5OlqOZHCbmvyy8Uc5ZdYeCYueh4B-WBbuTJmkmS__n7lOfXVK2gl4ECxYxyc-dHEzNKlPgo7gNhP-UXFyrYkMwwZBP9Qpu6T7AWh8S0gb2dH1CKEQm_mDaSdueItNlWVjdjHbpcrZO1tzoiUMixMavG6DlUiY6CxE-C4syJEN1fU3cysIrsK8cgRw0nPDs2F6rGWUsA6Yj5zYlWWibn_v6I5Tzz6ixikEMfDWh5pN4inLb9qJ-BOVwsSp1iRRB2_6zmpezqP_CqvHCbHmVs2f40XhUWcbIg8mzxSAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 01:13:13,802 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-15 01:13:18,241 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:13:18,241 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 01:13:18,242 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:13:18,573 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 01:13:18,574 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:13:18,574 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:13:18,575 - msal.application - DEBUG - Cache hit an AT
2025-06-15 01:13:18,575 - msal.telemetry - DEBUG - Generate or reuse correlation_id: c69c7036-ee60-45e9-9850-4bf8a045e979
2025-06-15 01:13:18,577 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-15 01:13:19,108 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 403 None
2025-06-15 01:13:19,552 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 01:13:50,605 - msal.telemetry - DEBUG - Generate or reuse correlation_id: d87d8ee5-c57a-4b27-9748-2cc23a982ed8
2025-06-15 01:13:51,627 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 01:13:51,627 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "LAQABIQEAAABVrSpeuWamRam2jAF1XRQE8NYvynyhb-ZKfr7aEHbPpZaBvkGMG0-si7pbe1wYxTz6so9qgD1hmRrnjv55jvgnrQ7WZykw95QGtveBQaLVRVpJI3N2JvlPY1jKE8ILLW3tDD67MMbIayG6cncX7U7jCc15CLvsHRn_zb4eXPyAVeF6xQVoWZlHWZ04WtKNm04QuLvQN6PomqqV7DxtOf6YilCqL1O4lS4pHUutlRApjEU6Wk-b7TcQW8IWf342roUtYAUowym1U3IxxuknXgMJx1NJP2hNuvSGVymkW0TomUouBkA4VQTbcHAXl4hwjIEMA1ghThGtFyiXUqf1hQtdc-w-f0SnFU3D-XuaW66o9iAA",
        "device_code": "LAQABIQEAAABVrSpeuWamRam2jAF1XRQE8NYvynyhb-ZKfr7aEHbPpZaBvkGMG0-si7pbe1wYxTz6so9qgD1hmRrnjv55jvgnrQ7WZykw95QGtveBQaLVRVpJI3N2JvlPY1jKE8ILLW3tDD67MMbIayG6cncX7U7jCc15CLvsHRn_zb4eXPyAVeF6xQVoWZlHWZ04WtKNm04QuLvQN6PomqqV7DxtOf6YilCqL1O4lS4pHUutlRApjEU6Wk-b7TcQW8IWf342roUtYAUowym1U3IxxuknXgMJx1NJP2hNuvSGVymkW0TomUouBkA4VQTbcHAXl4hwjIEMA1ghThGtFyiXUqf1hQtdc-w-f0SnFU3D-XuaW66o9iAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 01:13:56,735 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-15 01:14:01,883 - Worker - ERROR - Error Email Not Sent >> <EMAIL> (530, b'5.7.57 Client not authenticated to send mail. [LO2P265CA0222.GBRP265.PROD.OUTLOOK.COM 2025-06-15T00:14:01.752Z 08DDAB76B147A423]', '<EMAIL>')
2025-06-15 01:14:47,972 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-15 01:14:50,174 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:14:50,174 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 01:14:50,174 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:14:50,476 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 01:14:50,476 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:14:50,476 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:14:50,476 - msal.application - DEBUG - Found 1 RTs matching {'environment': 'login.microsoftonline.com', 'home_account_id': '********.9188040d-6c67-4c5b-b112-36a304b66dad', 'client_id': '9868b800-1d91-4573-9cf9-c2061096c3cb'}
2025-06-15 01:14:50,476 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 1f52b69f-02a3-4f95-bb4f-510ff1110912
2025-06-15 01:14:50,476 - msal.application - DEBUG - Cache attempts an RT
2025-06-15 01:14:50,699 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 01:14:50,699 - msal.application - DEBUG - Refresh failed. invalid_scope: AADSTS70011: The provided resource value for the input parameter 'scope' is not valid. Trace ID: 1ba453e1-d8d5-4b08-8879-4ea351c20300 Correlation ID: 1f52b69f-02a3-4f95-bb4f-510ff1110912 Timestamp: 2025-06-15 00:14:50Z
2025-06-15 01:14:50,777 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 01:14:51,063 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 01:14:57,220 - Worker - ERROR - Error <NAME_EMAIL> >> Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: Failed to create device flow. App password error: (535, b'5.7.139 Authentication unsuccessful, basic authentication is disabled. [PR2P264CA0046.FRAP264.PROD.OUTLOOK.COM 2025-06-15T00:14:57.101Z 08DDAB9BCD4E780B]')
2025-06-15 01:15:02,972 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 01:15:02,972 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 01:15:02,972 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 01:15:03,286 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 01:15:03,286 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 01:15:03,286 - msal.application - DEBUG - Broker enabled? None
2025-06-15 01:15:03,286 - msal.application - DEBUG - Found 1 RTs matching {'environment': 'login.microsoftonline.com', 'home_account_id': '********.9188040d-6c67-4c5b-b112-36a304b66dad', 'client_id': '9868b800-1d91-4573-9cf9-c2061096c3cb'}
2025-06-15 01:15:03,286 - msal.telemetry - DEBUG - Generate or reuse correlation_id: bd420907-0361-4d20-a8be-5bd26fc80518
2025-06-15 01:15:03,286 - msal.application - DEBUG - Cache attempts an RT
2025-06-15 01:15:03,604 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" ************-06-15 01:15:03,604 - msal.application - DEBUG - Refresh failed. invalid_scope: AADSTS70011: The provided resource value for the input parameter 'scope' is not valid. Trace ID: 74cb1e53-cffb-493f-a644-e37cb5681400 Correlation ID: bd420907-0361-4d20-a8be-5bd26fc80518 Timestamp: 2025-06-15 00:15:03Z
2025-06-15 01:15:03,682 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 01:15:04,003 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 01:15:10,145 - Worker - ERROR - Error <NAME_EMAIL> >> Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: Failed to create device flow. App password error: (535, b'5.7.139 Authentication unsuccessful, basic authentication is disabled. [CWLP123CA0002.GBRP123.PROD.OUTLOOK.COM 2025-06-15T00:15:10.017Z 08DDAB41E4CDA781]')
2025-06-15 01:15:27,836 - App - INFO - Application closing - cleaning up threads
2025-06-15 01:15:27,836 - App - ERROR - Error cleaning up email sending thread: wrapped C/C++ object of type QThread has been deleted
2025-06-15 18:13:55,009 - App - INFO - pywinauto is installed
2025-06-15 18:14:03,531 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 18:14:03,531 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 18:14:03,531 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 18:14:04,063 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 18:14:04,063 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 18:14:04,063 - msal.application - DEBUG - Broker enabled? None
2025-06-15 18:14:04,063 - msal.application - DEBUG - Found 1 RTs matching {'environment': 'login.microsoftonline.com', 'home_account_id': '********.9188040d-6c67-4c5b-b112-36a304b66dad', 'client_id': '9868b800-1d91-4573-9cf9-c2061096c3cb'}
2025-06-15 18:14:04,063 - msal.telemetry - DEBUG - Generate or reuse correlation_id: fdaee901-2d90-4f0f-9263-77e84668fd9d
2025-06-15 18:14:04,063 - msal.application - DEBUG - Cache attempts an RT
2025-06-15 18:14:04,577 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 18:14:04,578 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "refresh_token": "********",
        "scope": [
            "profile",
            "https://graph.microsoft.com/Mail.Send",
            "offline_access",
            "openid"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "refresh_token",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "skip_account_creation": true,
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 18:14:04,579 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-15 18:14:06,062 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 403 None
2025-06-15 18:14:06,608 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 18:15:23,724 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 46130a2e-9600-4bb9-831d-f827ead946dd
2025-06-15 18:15:24,769 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-15 18:15:24,770 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "DAQABIQEAAABVrSpeuWamRam2jAF1XRQEaVoelL4sWWUK0ZRAZvmzn7U_R0CNgvxSKxRolI-mFBMoWzLWZR2UePIqK3ND4EQhR36XYPLMTL7rMyGiWpVAHDB2y1iFU44LIvv3phsv8IN6q9OEbtqrl4cQeVOucQX7nh15hjXwRQN6Dvahafld7DiA4HjG4pIuGPdCrAgQTiu_h4qdt_ZwfRy7G1rOuOStrSQkyQy2iS7aZEyJt_L7MP974i9SZSW79NDobfskXAR-VqGXslu2ThKH2qshYJ0-qNHgM1D62poiZWKpgY3KL1a9zLsFE9HebZUPfj64s7jdYhLxa9mHL3d3ixCMTt8qICaVHk091ZPvdtB2oufjdyAA",
        "device_code": "DAQABIQEAAABVrSpeuWamRam2jAF1XRQEaVoelL4sWWUK0ZRAZvmzn7U_R0CNgvxSKxRolI-mFBMoWzLWZR2UePIqK3ND4EQhR36XYPLMTL7rMyGiWpVAHDB2y1iFU44LIvv3phsv8IN6q9OEbtqrl4cQeVOucQX7nh15hjXwRQN6Dvahafld7DiA4HjG4pIuGPdCrAgQTiu_h4qdt_ZwfRy7G1rOuOStrSQkyQy2iS7aZEyJt_L7MP974i9SZSW79NDobfskXAR-VqGXslu2ThKH2qshYJ0-qNHgM1D62poiZWKpgY3KL1a9zLsFE9HebZUPfj64s7jdYhLxa9mHL3d3ixCMTt8qICaVHk091ZPvdtB2oufjdyAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 18:15:29,865 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-15 18:15:35,003 - Worker - ERROR - Error Email Not Sent >> <EMAIL> (530, b'5.7.57 Client not authenticated to send mail. [PR1P264CA0065.FRAP264.PROD.OUTLOOK.COM 2025-06-15T17:15:33.688Z 08DDAAD94BD56BC6]', '<EMAIL>')
2025-06-15 18:22:49,223 - App - INFO - Application closing - cleaning up threads
2025-06-15 18:22:49,223 - App - ERROR - Error cleaning up email sending thread: wrapped C/C++ object of type QThread has been deleted
2025-06-15 18:23:03,778 - App - INFO - pywinauto is installed
2025-06-15 18:23:09,587 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 18:23:09,587 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 18:23:09,587 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 18:23:09,902 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 18:23:09,903 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 18:23:09,903 - msal.application - DEBUG - Broker enabled? None
2025-06-15 18:23:09,997 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 18:23:16,219 - Worker - ERROR - Error <NAME_EMAIL> >> Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: Failed to create device flow: You cannot use any scope value that is reserved.
Your input: ['offline_access', 'https://outlook.office.com/SMTP.Send']
The reserved list: ['openid', 'offline_access', 'profile']. App password error: (535, b'5.7.139 Authentication unsuccessful, basic authentication is disabled. [PR0P264CA0067.FRAP264.PROD.OUTLOOK.COM 2025-06-15T17:23:14.905Z 08DDA9F48BA7C294]')
2025-06-15 18:31:03,968 - App - INFO - pywinauto is installed
2025-06-15 18:31:07,950 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 18:31:07,950 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 18:31:07,950 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 18:31:08,451 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 18:31:08,451 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 18:31:08,451 - msal.application - DEBUG - Broker enabled? None
2025-06-15 18:31:08,577 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-15 18:31:09,004 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 18:31:45,781 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 9638c41e-e612-4e49-b8f8-364e6fd23feb
2025-06-15 18:31:47,144 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3716
2025-06-15 18:31:47,144 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "CAQABIQEAAABVrSpeuWamRam2jAF1XRQEsCINbBJ6-n_jF9JxND1LmJQm4wUoQedIl2IB3OQ-JHO6oyp5BoNSZJ54ZGJ-oT38itDGMtTK-6NZ35IW5pgXF5be4Vwox_AI-mlfWQ7E1riB_Zkm03ho4IsS6QfDa4weoudnzGFedmDIFh0c9H8X_2AHoCP-iYumsfjav_3zowcy5wYlVWkRPgD80-g6VHO_Xi7FzAGTTgmQXU9EEdKZfeopwTwaLaOda757y6TQUf793hSTKQwI52IFY_HHvx0UEargLkaoSpaAFKEQSxQ3INmNQpb0mu8hKG7t0G6H6yty0Jic-O6FJnNqZrm9SouqxHzSQqRWXyaihBOA5eR-lyAA",
        "device_code": "CAQABIQEAAABVrSpeuWamRam2jAF1XRQEsCINbBJ6-n_jF9JxND1LmJQm4wUoQedIl2IB3OQ-JHO6oyp5BoNSZJ54ZGJ-oT38itDGMtTK-6NZ35IW5pgXF5be4Vwox_AI-mlfWQ7E1riB_Zkm03ho4IsS6QfDa4weoudnzGFedmDIFh0c9H8X_2AHoCP-iYumsfjav_3zowcy5wYlVWkRPgD80-g6VHO_Xi7FzAGTTgmQXU9EEdKZfeopwTwaLaOda757y6TQUf793hSTKQwI52IFY_HHvx0UEargLkaoSpaAFKEQSxQ3INmNQpb0mu8hKG7t0G6H6yty0Jic-O6FJnNqZrm9SouqxHzSQqRWXyaihBOA5eR-lyAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://outlook.office.com/SMTP.Send https://outlook.office.com/Mail.Send",
        "token_type": "Bearer"
    },
    "scope": [
        "https://outlook.office.com/SMTP.Send",
        "https://outlook.office.com/Mail.Send"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 18:31:52,224 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-15 18:31:57,353 - Worker - ERROR - Error Email Not Sent >> <EMAIL> (530, b'5.7.57 Client not authenticated to send mail. [MA2P292CA0026.ESPP292.PROD.OUTLOOK.COM 2025-06-15T17:31:56.046Z 08DDAA2DB368D3C1]', '<EMAIL>')
2025-06-15 18:58:43,539 - App - INFO - pywinauto is installed
2025-06-15 18:58:47,082 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 18:58:47,083 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 18:58:47,085 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 18:58:47,565 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 18:58:47,565 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 18:58:47,565 - msal.application - DEBUG - Broker enabled? None
2025-06-15 18:58:47,565 - msal.application - DEBUG - Cache hit an AT
2025-06-15 18:58:47,565 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 1598054a-8585-4f17-bbec-6faa5baa8db8
2025-06-15 18:58:47,917 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 18:59:20,318 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 0203f2c2-65d7-4628-a047-f7a5ba22b9dd
2025-06-15 18:59:21,257 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3716
2025-06-15 18:59:21,257 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "CAQABIQEAAABVrSpeuWamRam2jAF1XRQE4_hXjdhIfCZM_iI5Oya61Jgdi05Ml-_UWsmv1ErM4bHh0F57HWXcG60eCdtLD_caquPKJtyoK7-BmMYulNPf1I1EiLCeuoETACm2iQ6xJ91h3PWuclCdqIh3jqUii4orWQ54FRhZ9DtzkTqjUifc37hQ9nQE3YqvHaAfUHzRmR86wcl7a7PUWLWIvGcQCclUpHZAN6K1CEvQshjISXwdn_ferMnZZpR3ABZFLhWUoG452cNostULyQmqbQEZ19nkkJeHCABWmCChRkdEAPiQCyDEDj5-iGlA0Nojhx9rWR8GDApeL5aCcbOCUKoP2bKLI6z1FgYRqboj51c3Uzm8iSAA",
        "device_code": "CAQABIQEAAABVrSpeuWamRam2jAF1XRQE4_hXjdhIfCZM_iI5Oya61Jgdi05Ml-_UWsmv1ErM4bHh0F57HWXcG60eCdtLD_caquPKJtyoK7-BmMYulNPf1I1EiLCeuoETACm2iQ6xJ91h3PWuclCdqIh3jqUii4orWQ54FRhZ9DtzkTqjUifc37hQ9nQE3YqvHaAfUHzRmR86wcl7a7PUWLWIvGcQCclUpHZAN6K1CEvQshjISXwdn_ferMnZZpR3ABZFLhWUoG452cNostULyQmqbQEZ19nkkJeHCABWmCChRkdEAPiQCyDEDj5-iGlA0Nojhx9rWR8GDApeL5aCcbOCUKoP2bKLI6z1FgYRqboj51c3Uzm8iSAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://outlook.office.com/SMTP.Send https://outlook.office.com/Mail.Send",
        "token_type": "Bearer"
    },
    "scope": [
        "https://outlook.office.com/SMTP.Send",
        "https://outlook.office.com/Mail.Send"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 18:59:32,575 - Worker - ERROR - Error <NAME_EMAIL> >> Both OAuth2 and app password authentication failed. OAuth2 error: SMTP server doesn't support XOAUTH2 or unexpected response: 503 b'5.5.2 Send hello first [PA7P264CA0003.FRAP264.PROD.OUTLOOK.COM 2025-06-15T17:59:25.061Z 08DDAC004DC5E705]'. App password error: (535, b'5.7.139 Authentication unsuccessful, basic authentication is disabled. [PA7P264CA0003.FRAP264.PROD.OUTLOOK.COM 2025-06-15T17:59:31.264Z 08DDAC004DC5E705]')
2025-06-15 19:16:52,483 - App - INFO - Application closing - cleaning up threads
2025-06-15 19:16:52,483 - App - ERROR - Error cleaning up email sending thread: wrapped C/C++ object of type QThread has been deleted
2025-06-15 19:16:55,111 - App - INFO - pywinauto is installed
2025-06-15 19:17:00,938 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 19:17:00,938 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 19:17:00,948 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 19:17:01,351 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 19:17:01,351 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 19:17:01,351 - msal.application - DEBUG - Broker enabled? None
2025-06-15 19:17:01,351 - msal.application - DEBUG - Cache hit an AT
2025-06-15 19:17:01,351 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 25c3c021-bc7b-48a1-9a12-2bcb06c28752
2025-06-15 19:17:01,784 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 19:19:56,940 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 663a62c7-3ff4-4e21-89de-55837fbbad57
2025-06-15 19:19:56,943 - urllib3.connectionpool - DEBUG - Resetting dropped connection: login.microsoftonline.com
2025-06-15 19:19:59,099 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3716
2025-06-15 19:19:59,114 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "GAQABIQEAAABVrSpeuWamRam2jAF1XRQE1K4LBG3qX5opWXfByXSDLdrpM_4vbz2psRX6E6fBMoFGOV4F3EJc51FX-zwW3a_L8MmPChUPf9yBvyT25bAha5pF8TTR4WRyPkFzyx8MxcmdmdheFPdcBNJmHdOeIBiph2L7iAJnP6a4qqmsdrK-ubNpbJJ5Qexe32m2DYgq6pxGAMHevQdCXBaSyMqN5iEWGmScnvcAn59XuFmWWV7jvSHk14oq2pkTHGu60S6cu6iGOxIeGRVh6_hRTapBXbBLyOgWAjfn3MJ4qBN7QP_dg6Ii3sr5WylONEnXNwFPpt9Qdzda_hUVtrlVfTrMzl9fNrMLcKNr9KKyXjoZh_FxDCAA",
        "device_code": "GAQABIQEAAABVrSpeuWamRam2jAF1XRQE1K4LBG3qX5opWXfByXSDLdrpM_4vbz2psRX6E6fBMoFGOV4F3EJc51FX-zwW3a_L8MmPChUPf9yBvyT25bAha5pF8TTR4WRyPkFzyx8MxcmdmdheFPdcBNJmHdOeIBiph2L7iAJnP6a4qqmsdrK-ubNpbJJ5Qexe32m2DYgq6pxGAMHevQdCXBaSyMqN5iEWGmScnvcAn59XuFmWWV7jvSHk14oq2pkTHGu60S6cu6iGOxIeGRVh6_hRTapBXbBLyOgWAjfn3MJ4qBN7QP_dg6Ii3sr5WylONEnXNwFPpt9Qdzda_hUVtrlVfTrMzl9fNrMLcKNr9KKyXjoZh_FxDCAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://outlook.office.com/SMTP.Send https://outlook.office.com/Mail.Send",
        "token_type": "Bearer"
    },
    "scope": [
        "https://outlook.office.com/SMTP.Send",
        "https://outlook.office.com/Mail.Send"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 19:20:00,865 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-15 19:20:03,416 - Worker - ERROR - Error Email Not Sent >> <EMAIL> (554, b'5.2.0 STOREDRV.Submission.Exception:OutboundSpamException; Failed to process message due to a permanent exception with message [BeginDiagnosticData]WASCL UserAction verdict is not None. Actual verdict is Suspend, ShowTierUpgrade. OutboundSpamException: WASCL UserAction verdict is not None. Actual verdict is Suspend, ShowTierUpgrade.[EndDiagnosticData] [Hostname=MN0PR11MB6232.namprd11.prod.outlook.com]')
2025-06-15 19:25:41,494 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 19:25:41,494 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 19:25:41,495 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 19:25:41,897 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 19:25:41,897 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 19:25:41,898 - msal.application - DEBUG - Broker enabled? None
2025-06-15 19:25:41,899 - msal.application - DEBUG - Cache hit an AT
2025-06-15 19:25:41,900 - msal.telemetry - DEBUG - Generate or reuse correlation_id: f60ee240-719e-4846-b4ab-f03b01c6f325
2025-06-15 19:25:42,246 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 19:28:50,984 - App - INFO - pywinauto is installed
2025-06-15 19:32:21,311 - App - INFO - Application closing - cleaning up threads
2025-06-15 19:32:21,312 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-15 19:53:36,391 - App - INFO - pywinauto is installed
2025-06-15 19:53:38,955 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 19:53:38,955 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 19:53:38,957 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 19:53:39,475 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 19:53:39,475 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 19:53:39,475 - msal.application - DEBUG - Broker enabled? None
2025-06-15 19:53:39,475 - msal.application - DEBUG - Cache hit an AT
2025-06-15 19:53:39,475 - msal.telemetry - DEBUG - Generate or reuse correlation_id: b9d82290-9425-43d7-8a18-29d890e4534b
2025-06-15 19:53:39,847 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 19:58:41,023 - Worker - ERROR - Error <NAME_EMAIL> >> Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: . App password error: Connection unexpectedly closed
2025-06-15 19:58:53,172 - App - INFO - pywinauto is installed
2025-06-15 19:59:03,630 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-15 19:59:03,630 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-15 19:59:03,634 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-15 19:59:06,571 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-15 19:59:06,571 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-15 19:59:06,571 - msal.application - DEBUG - Broker enabled? None
2025-06-15 19:59:06,571 - msal.application - DEBUG - Cache hit an AT
2025-06-15 19:59:06,571 - msal.telemetry - DEBUG - Generate or reuse correlation_id: f2cf8be4-b6db-4a68-8e75-5b5daee5882a
2025-06-15 19:59:07,681 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-15 19:59:58,582 - msal.telemetry - DEBUG - Generate or reuse correlation_id: f39de4d5-1f79-4938-a482-fab904bdd619
2025-06-15 19:59:59,040 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3656
2025-06-15 19:59:59,040 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "LAQABIQEAAABVrSpeuWamRam2jAF1XRQERsbajwb9S63YgIX3JTQK7ZuhD8WvxTDaYrWsw7AWLnrZRzGUjCmZDz7AfOEcg02mSk1HYJEFIgOs4GlSqqiU60ClTgTiwmkMprola7OlkjWq-iL3KHDlcBCS9TeQTDpeZqtVNwmlCC0n9peOQe6EwTcMTtbx-o387xtaic6IbrglZhvwMjBIms9Vy0pJ3J8sCefDgMTyQ_lDIvYDJ6l4UlBcYgB3hI1-UMdABJPfadYFFoRbfOlOKcyv9diPRwAvp2IhewcF1XhPOhHFDCi36DnyyVeUXei4YYJRcmPa6B8NoMZoJgfAgbFappev8AAQuyhu40ezPV_Qg1sx-lkSdyAA",
        "device_code": "LAQABIQEAAABVrSpeuWamRam2jAF1XRQERsbajwb9S63YgIX3JTQK7ZuhD8WvxTDaYrWsw7AWLnrZRzGUjCmZDz7AfOEcg02mSk1HYJEFIgOs4GlSqqiU60ClTgTiwmkMprola7OlkjWq-iL3KHDlcBCS9TeQTDpeZqtVNwmlCC0n9peOQe6EwTcMTtbx-o387xtaic6IbrglZhvwMjBIms9Vy0pJ3J8sCefDgMTyQ_lDIvYDJ6l4UlBcYgB3hI1-UMdABJPfadYFFoRbfOlOKcyv9diPRwAvp2IhewcF1XhPOhHFDCi36DnyyVeUXei4YYJRcmPa6B8NoMZoJgfAgbFappev8AAQuyhu40ezPV_Qg1sx-lkSdyAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://outlook.office.com/SMTP.Send",
        "token_type": "Bearer"
    },
    "scope": [
        "https://outlook.office.com/SMTP.Send"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-15 19:59:59,531 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-15 20:00:19,624 - Worker - INFO - Email Sent using >> rinad.sahili@hotmail.<NAME_EMAIL>
2025-06-15 20:19:04,901 - App - INFO - Application closing - cleaning up threads
2025-06-15 20:19:04,901 - App - ERROR - Error cleaning up email sending thread: wrapped C/C++ object of type QThread has been deleted
2025-06-16 18:40:38,306 - App - INFO - pywinauto is installed
2025-06-16 18:42:41,517 - App - INFO - Application closing - cleaning up threads
2025-06-16 18:42:41,517 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-16 22:41:49,209 - App - INFO - pywinauto is installed
2025-06-16 22:41:58,939 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-16 22:41:58,939 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-16 22:41:58,939 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-16 22:41:59,429 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-16 22:41:59,429 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-16 22:41:59,429 - msal.application - DEBUG - Broker enabled? None
2025-06-16 22:41:59,429 - msal.application - DEBUG - Found 1 RTs matching {'environment': 'login.microsoftonline.com', 'home_account_id': '********.9188040d-6c67-4c5b-b112-36a304b66dad', 'client_id': '9868b800-1d91-4573-9cf9-c2061096c3cb'}
2025-06-16 22:41:59,429 - msal.telemetry - DEBUG - Generate or reuse correlation_id: ad71445f-a01d-45d7-9ac2-94d98e0e58ea
2025-06-16 22:41:59,429 - msal.application - DEBUG - Cache attempts an RT
2025-06-16 22:41:59,852 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3716
2025-06-16 22:41:59,852 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "refresh_token": "********",
        "scope": [
            "profile",
            "openid",
            "offline_access",
            "https://outlook.office.com/SMTP.Send"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "refresh_token",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "scope": "https://outlook.office.com/SMTP.Send https://outlook.office.com/Mail.Send",
        "token_type": "Bearer"
    },
    "scope": [
        "https://outlook.office.com/SMTP.Send",
        "https://outlook.office.com/Mail.Send"
    ],
    "skip_account_creation": true,
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-16 22:42:00,339 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-16 22:42:25,973 - App - INFO - Application closing - cleaning up threads
2025-06-16 22:42:25,973 - App - INFO - Stopping email sending thread
2025-06-16 22:42:27,974 - App - WARNING - Email sending thread did not terminate within timeout
2025-06-16 22:43:26,173 - App - INFO - pywinauto is installed
2025-06-16 22:43:42,677 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-16 22:43:42,677 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-16 22:43:42,679 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-16 22:43:43,135 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-16 22:43:43,136 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-16 22:43:43,136 - msal.application - DEBUG - Broker enabled? None
2025-06-16 22:43:43,229 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-16 22:43:43,695 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-16 22:44:38,411 - msal.telemetry - DEBUG - Generate or reuse correlation_id: ef0fd2b6-cefb-4e55-a947-61509a61cbf3
2025-06-16 22:44:39,137 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3656
2025-06-16 22:44:39,137 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "LAQABIQEAAABVrSpeuWamRam2jAF1XRQEb_peYffhiGa_BhXDyqhvkwFlnQGrNMCYG-Z3etYya59pfSE3IW8vS8Q2cByJZcBf26RpUiay3Cg6kLkIKs7lBjOcoK1KVGnQoy6YoQgJiB9Xws8DYUQhiZ_DQDRMA9OwF-_PB8eZ_iHodmSzoXu076QixUClcNyQwtJQG008Dx1USEf4PR6QfePuazR95WFjAo8c0oif_hrA2iJhN09aV2A5xSA5dSQ1ATydaLA230UzUx-KHKZKmWnj_u1juQbipTEzsQ5BFiq8E1U4TvcLOcgsosQek6Tct_ayN0bvaEID--hgEm18QUUsNE1g6yPhYEgblTFnYWclfxlYVwSjNiAA",
        "device_code": "LAQABIQEAAABVrSpeuWamRam2jAF1XRQEb_peYffhiGa_BhXDyqhvkwFlnQGrNMCYG-Z3etYya59pfSE3IW8vS8Q2cByJZcBf26RpUiay3Cg6kLkIKs7lBjOcoK1KVGnQoy6YoQgJiB9Xws8DYUQhiZ_DQDRMA9OwF-_PB8eZ_iHodmSzoXu076QixUClcNyQwtJQG008Dx1USEf4PR6QfePuazR95WFjAo8c0oif_hrA2iJhN09aV2A5xSA5dSQ1ATydaLA230UzUx-KHKZKmWnj_u1juQbipTEzsQ5BFiq8E1U4TvcLOcgsosQek6Tct_ayN0bvaEID--hgEm18QUUsNE1g6yPhYEgblTFnYWclfxlYVwSjNiAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://outlook.office.com/SMTP.Send",
        "token_type": "Bearer"
    },
    "scope": [
        "https://outlook.office.com/SMTP.Send"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-16 22:44:40,309 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-16 22:45:00,467 - Worker - INFO - Email Sent using >> rinad.sahili@hotmail.<NAME_EMAIL>
2025-06-16 22:45:06,519 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-16 22:45:06,519 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-16 22:45:06,523 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-16 22:45:06,854 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-16 22:45:06,854 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-16 22:45:06,854 - msal.application - DEBUG - Broker enabled? None
2025-06-16 22:45:06,859 - msal.application - DEBUG - Cache hit an AT
2025-06-16 22:45:06,859 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 8bcf6513-36c7-4677-9ab3-b5164d75eadb
2025-06-16 22:45:07,309 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-16 22:50:50,050 - App - INFO - Application closing - cleaning up threads
2025-06-16 22:50:50,050 - App - INFO - Stopping email sending thread
2025-06-16 22:50:52,061 - App - WARNING - Email sending thread did not terminate within timeout
2025-06-16 22:51:00,116 - App - INFO - pywinauto is installed
2025-06-16 22:51:02,601 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-16 22:51:02,601 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-16 22:51:02,604 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-16 22:51:03,020 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-16 22:51:03,020 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-16 22:51:03,020 - msal.application - DEBUG - Broker enabled? None
2025-06-16 22:51:03,020 - msal.application - DEBUG - Cache hit an AT
2025-06-16 22:51:03,020 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 217edaa6-42d4-4905-b6ae-70feb172faa1
2025-06-16 22:51:03,971 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-16 22:51:05,850 - Worker - INFO - Email Sent using >> rinad.sahili@hotmail.<NAME_EMAIL>
2025-06-16 22:52:26,823 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-16 22:52:26,823 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-16 22:52:26,823 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-16 22:52:27,161 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-16 22:52:27,161 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-16 22:52:27,161 - msal.application - DEBUG - Broker enabled? None
2025-06-16 22:52:27,161 - msal.application - DEBUG - Cache hit an AT
2025-06-16 22:52:27,161 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 1f6e63e0-b811-4844-a86f-0ceb20a242bc
2025-06-16 22:52:28,024 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-16 22:52:29,474 - Worker - INFO - Email Sent using >> rinad.sahili@hotmail.<NAME_EMAIL>
