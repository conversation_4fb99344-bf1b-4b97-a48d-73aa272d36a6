import smtplib
import ssl
from email.mime.text import MIME<PERSON>ext
from email.mime.multipart import MIMEMultipart
from email.utils import formataddr
import os, sys, ctypes, logging, json, time, datetime
from time import sleep
from functools import partial
import base64
import re
from PyQt6.QtWidgets import QApplication,QDialog,QVBoxLayout,QMainWindow, QPlainTextEdit, QVBoxLayout, QPushButton, QMessageBox
from PyQt6.QtCore import QUrl,Qt, QObject, pyqtSignal, QThread, QTimer

# Optional WebEngine imports for HTML preview
try:
    from PyQt6.QtWebEngineWidgets import QWebEngineView
    from PyQt6.QtWebEngineCore import QWebEngineSettings
    WEBENGINE_AVAILABLE = True
except ImportError:
    WEBENGINE_AVAILABLE = False
from PyQt6 import uic, QtCore
from PyQt6.QtGui import QIcon, QKeySequence, QShortcut

# OAuth2 imports for Microsoft 365 authentication
try:
    import msal
    MSAL_AVAILABLE = True
except ImportError:
    MSAL_AVAILABLE = False


myappid = 'mycompany.myproduct.subproduct.version'
ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")


class OAuth2SMTPAuth:
    """
    OAuth2 authentication handler for Microsoft 365 SMTP
    """

    def __init__(self):
        self.client_id = None
        self.client_secret = None
        self.tenant_id = None
        self.authority = None
        # Use the correct scope for personal accounts - SMTP.Send scope is required for SMTP authentication
        # Note: offline_access may be restricted in some Azure app configurations
        self.scope = ["https://outlook.office.com/SMTP.Send"]  # Correct scope for personal accounts SMTP
        self.account_type = "personal"  # "personal" or "business"

    def configure_oauth2(self, client_id=None, client_secret=None, tenant_id=None, account_type=None):
        """
        Configure OAuth2 credentials. If not provided, will try to load from environment or config file.
        """
        # Try to get from parameters first
        self.client_id = client_id
        self.client_secret = client_secret
        self.tenant_id = tenant_id
        if account_type:
            self.account_type = account_type

        # If not provided, try environment variables
        if not self.client_id:
            self.client_id = os.getenv('MICROSOFT_CLIENT_ID')
        if not self.client_secret:
            self.client_secret = os.getenv('MICROSOFT_CLIENT_SECRET')
        if not self.tenant_id:
            self.tenant_id = os.getenv('MICROSOFT_TENANT_ID')
        if not account_type and os.getenv('MICROSOFT_ACCOUNT_TYPE'):
            self.account_type = os.getenv('MICROSOFT_ACCOUNT_TYPE')

        # Try to load from config file if still not found
        if not all([self.client_id, self.client_secret]):
            self._load_from_config()

        # Set authority and scope based on account type
        if self.account_type == "business" and self.tenant_id:
            self.authority = f"https://login.microsoftonline.com/{self.tenant_id}"
            self.scope = ["https://outlook.office365.com/.default"]
        else:
            # Personal account (default) - use 'consumers' endpoint for personal Microsoft accounts
            self.authority = "https://login.microsoftonline.com/consumers"
            # Keep the scope we set in __init__ for SMTP
            self.account_type = "personal"

    def _get_optimal_scope_for_personal_account(self):
        """
        Get the optimal scope for personal accounts
        Start with basic scope to avoid reserved scope issues
        """
        # Always start with the basic scope that we know works
        base_scope = ["https://outlook.office.com/SMTP.Send"]

        print(f"[SCOPE] Using basic scope for personal account: {base_scope}")
        print("[SCOPE] Note: offline_access may be restricted in some Azure app configurations")

        return base_scope

    def _load_from_config(self):
        """Load OAuth2 configuration from config file"""
        config_file = os.path.join(home, "oauth2_config.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    self.client_id = config.get('client_id')
                    self.client_secret = config.get('client_secret')
                    self.tenant_id = config.get('tenant_id')
                    self.account_type = config.get('account_type', 'personal')
            except Exception as e:
                logging.warning(f"Failed to load OAuth2 config: {e}")

    def _get_token_cache(self):
        """Get or create a persistent token cache"""
        try:
            import msal
            import atexit

            # Create cache file path in the application directory
            cache_filename = os.path.join(home, "oauth2_token_cache.bin")
            scope_marker_filename = os.path.join(home, "oauth2_scope_marker.txt")

            # Check if scope has changed and clear cache if needed
            current_scope_str = ",".join(sorted(self.scope))
            scope_changed = False

            if os.path.exists(scope_marker_filename):
                try:
                    with open(scope_marker_filename, "r") as f:
                        old_scope_str = f.read().strip()
                    if old_scope_str != current_scope_str:
                        print(f"[CACHE] Scope changed from '{old_scope_str}' to '{current_scope_str}' - clearing cache")
                        scope_changed = True
                except Exception as e:
                    print(f"[CACHE] Warning: Failed to read scope marker: {e}")
                    scope_changed = True
            else:
                print(f"[CACHE] No scope marker found - will create one")
                scope_changed = True

            # Clear cache if scope changed
            if scope_changed:
                if os.path.exists(cache_filename):
                    try:
                        os.remove(cache_filename)
                        print(f"[CACHE] Cleared old token cache due to scope change")
                    except Exception as e:
                        print(f"[CACHE] Warning: Failed to clear old cache: {e}")

                # Save new scope marker
                try:
                    with open(scope_marker_filename, "w") as f:
                        f.write(current_scope_str)
                    print(f"[CACHE] Saved new scope marker: {current_scope_str}")
                except Exception as e:
                    print(f"[CACHE] Warning: Failed to save scope marker: {e}")

            # Create a serializable token cache
            cache = msal.SerializableTokenCache()

            # Load existing cache if it exists
            if os.path.exists(cache_filename):
                try:
                    with open(cache_filename, "r") as cache_file:
                        cache.deserialize(cache_file.read())
                    print(f"[CACHE] Loaded existing token cache from {cache_filename}")
                except Exception as e:
                    print(f"[CACHE] Warning: Failed to load cache file: {e}")
            else:
                print(f"[CACHE] No existing cache file found at {cache_filename}")

            # Store cache filename for immediate saving
            cache._cache_filename = cache_filename

            # Register a callback to save cache when the application exits
            def save_cache():
                if cache.has_state_changed:
                    try:
                        with open(cache_filename, "w") as cache_file:
                            cache_file.write(cache.serialize())
                        print(f"[CACHE] Token cache saved to {cache_filename}")
                    except Exception as e:
                        print(f"[CACHE] Warning: Failed to save cache: {e}")

            atexit.register(save_cache)
            return cache

        except Exception as e:
            print(f"[CACHE] Warning: Failed to create token cache: {e}")
            return None

    def _save_token_cache_immediately(self, token_cache):
        """Save token cache immediately after successful authentication"""
        if not token_cache or not hasattr(token_cache, '_cache_filename'):
            print("[CACHE] No cache or cache filename available for immediate save")
            return False

        try:
            if token_cache.has_state_changed:
                with open(token_cache._cache_filename, "w") as cache_file:
                    cache_file.write(token_cache.serialize())
                print(f"[CACHE] ✅ Token cache saved immediately to {token_cache._cache_filename}")
                return True
            else:
                print("[CACHE] No cache changes to save")
                return True
        except Exception as e:
            print(f"[CACHE] ⚠️ Failed to save cache immediately: {e}")
            return False

    def _validate_token(self, token, email=None):
        """Validate if the token is still valid - handles both JWT and opaque tokens"""
        try:
            import base64
            import json
            import time

            print(f"[TOKEN] Validating token - length: {len(token)}, starts with: {token[:20]}...")

            # Microsoft OAuth2 tokens can be in different formats:
            # 1. JWT tokens (3 parts separated by dots)
            # 2. Opaque tokens (random strings)
            # 3. Other proprietary formats

            # Check if it looks like a JWT token
            parts = token.split('.')
            if len(parts) == 3:
                print("[TOKEN] Token appears to be in JWT format, attempting JWT validation...")
                try:
                    # Decode the payload (second part)
                    payload_part = parts[1]
                    # Add padding if needed
                    padding = 4 - len(payload_part) % 4
                    if padding != 4:
                        payload_part += '=' * padding

                    payload_bytes = base64.urlsafe_b64decode(payload_part)
                    payload = json.loads(payload_bytes.decode('utf-8'))

                    print(f"[TOKEN] JWT payload decoded successfully")

                    # Check if token is expired
                    current_time = int(time.time())
                    exp_time = payload.get('exp', 0)

                    if exp_time and current_time >= exp_time:
                        print(f"[TOKEN] JWT token expired: exp={exp_time}, current={current_time}")
                        return False

                    # Check if email matches (if provided)
                    if email:
                        token_email = payload.get('email') or payload.get('preferred_username', '') or payload.get('upn', '')
                        if token_email and token_email.lower() != email.lower():
                            print(f"[TOKEN] JWT token email mismatch: token={token_email}, expected={email}")
                            return False

                    if exp_time:
                        time_left = exp_time - current_time
                        print(f"[TOKEN] ✅ JWT token validation successful - expires in {time_left} seconds")
                    else:
                        print(f"[TOKEN] ✅ JWT token validation successful - no expiration found")
                    return True

                except Exception as jwt_error:
                    print(f"[TOKEN] JWT validation failed: {jwt_error}")
                    # Fall through to opaque token handling

            # Handle opaque/non-JWT tokens (common for Microsoft personal accounts)
            print("[TOKEN] Token is not JWT format or JWT validation failed - treating as opaque token")

            # For opaque tokens, we do basic validation:
            # 1. Check if token is not empty
            # 2. Check if token has reasonable length
            # 3. Check if token contains valid characters

            if not token or len(token.strip()) == 0:
                print("[TOKEN] Token is empty or whitespace")
                return False

            # Microsoft access tokens are typically 1000-2000 characters
            if len(token) < 50:
                print(f"[TOKEN] Token too short ({len(token)} chars) - likely invalid")
                return False

            if len(token) > 10000:
                print(f"[TOKEN] Token too long ({len(token)} chars) - likely invalid")
                return False

            # Check for basic token characteristics (base64-like characters)
            import re
            if not re.match(r'^[A-Za-z0-9+/=_-]+$', token):
                print("[TOKEN] Token contains invalid characters")
                return False

            print(f"[TOKEN] ✅ Opaque token validation successful - {len(token)} chars, appears valid")
            print("[TOKEN] Note: Opaque tokens cannot be validated for expiration - will be validated by SMTP server")
            return True

        except Exception as e:
            print(f"[TOKEN] Token validation error: {e}")
            # If validation completely fails, assume token is valid and let SMTP server handle it
            print("[TOKEN] Validation failed but assuming token is valid - SMTP server will validate")
            return True

    def get_access_token(self, email=None, progress_callback=None):
        """
        Get OAuth2 access token using appropriate flow based on account type
        """
        if not MSAL_AVAILABLE:
            raise Exception("MSAL library not available. Install with: pip install msal")

        if not all([self.client_id, self.authority]):
            raise Exception("OAuth2 not properly configured. Missing client_id or authority")

        try:
            if self.account_type == "business" and self.client_secret:
                # Business account - use client credentials flow
                app = msal.ConfidentialClientApplication(
                    self.client_id,
                    authority=self.authority,
                    client_credential=self.client_secret,
                )
                result = app.acquire_token_for_client(scopes=self.scope)
            else:
                # Personal account - use device code flow (interactive) with persistent cache

                # Determine optimal scope for personal account (with or without offline_access)
                optimal_scope = self._get_optimal_scope_for_personal_account()
                print(f"[SCOPE] Using optimal scope for personal account: {optimal_scope}")

                # Update self.scope to the optimal scope
                self.scope = optimal_scope

                # Get persistent token cache
                token_cache = self._get_token_cache()

                app = msal.PublicClientApplication(
                    self.client_id,
                    authority=self.authority,
                    token_cache=token_cache  # Add persistent cache
                )

                # Try to get token silently first (from cache)
                accounts = app.get_accounts()
                print(f"[CACHE] Found {len(accounts)} cached accounts")

                if accounts:
                    print(f"[CACHE] Account details: {[acc.get('username', 'unknown') for acc in accounts]}")
                    if progress_callback:
                        progress_callback("   Found cached account, attempting silent token acquisition...")

                    # Try to get token silently for the first account
                    print(f"[CACHE] Attempting silent token acquisition for account: {accounts[0].get('username', 'unknown')}")
                    result = app.acquire_token_silent(self.scope, account=accounts[0])

                    if result and "access_token" in result:
                        print(f"[CACHE] Silent token acquisition successful - token length: {len(result['access_token'])}")
                        # Validate the token before using it
                        print("[TOKEN] Validating cached token...")

                        # For Microsoft personal accounts, we'll be more lenient with validation
                        # since MSAL already validated the token when retrieving it from cache
                        is_valid = self._validate_token(result['access_token'], email)

                        if is_valid:
                            if progress_callback:
                                progress_callback("   ✅ OAuth2 token retrieved from cache and validated")
                            print("[CACHE] ✅ Successfully retrieved and validated token from cache - NO DIALOG NEEDED")
                            return result['access_token']
                        else:
                            # For personal accounts, if our validation fails but MSAL retrieved the token,
                            # it's likely still valid - MSAL has its own validation
                            print("[TOKEN] Our token validation failed, but MSAL retrieved it successfully")
                            print("[TOKEN] For personal accounts, trusting MSAL validation and using cached token")
                            if progress_callback:
                                progress_callback("   ✅ OAuth2 token retrieved from cache (MSAL validated)")
                            print("[CACHE] ✅ Using cached token despite validation failure - MSAL validated it")
                            return result['access_token']
                    else:
                        print(f"[CACHE] Silent token acquisition failed")
                        if result:
                            print(f"[CACHE] Failed result keys: {list(result.keys())}")
                            if 'error' in result:
                                print(f"[CACHE] Error: {result['error']}")
                            if 'error_description' in result:
                                print(f"[CACHE] Error description: {result['error_description']}")
                        else:
                            print("[CACHE] No result returned from silent acquisition")
                else:
                    print("[CACHE] No cached accounts found")

                # If silent acquisition fails, use device code flow
                if progress_callback:
                    progress_callback("   No cached token found, requesting new authentication...")
                print("[CACHE] No valid cached token, initiating new authentication")

                print(f"[DEVICE_FLOW] Initiating device flow with scope: {self.scope}")
                print(f"[DEVICE_FLOW] Authority: {self.authority}")
                print(f"[DEVICE_FLOW] Client ID: {self.client_id[:12] if self.client_id else 'None'}...")

                try:
                    flow = app.initiate_device_flow(scopes=self.scope)
                    print(f"[DEVICE_FLOW] Device flow result keys: {list(flow.keys()) if isinstance(flow, dict) else 'Not a dict'}")

                    if "user_code" not in flow:
                        print(f"[DEVICE_FLOW] Device flow failed: {flow}")
                        raise Exception(f"Failed to create device flow: {flow}")
                    else:
                        print(f"[DEVICE_FLOW] Device flow created successfully with code: {flow.get('user_code', 'N/A')}")

                except Exception as device_flow_error:
                    error_msg = str(device_flow_error).lower()
                    if "reserved" in error_msg and "offline_access" in error_msg:
                        # Retry with basic scope if offline_access is causing issues
                        print(f"[DEVICE_FLOW] Retrying with basic scope due to reserved scope error")
                        basic_scope = ["https://outlook.office.com/SMTP.Send"]
                        self.scope = basic_scope
                        try:
                            flow = app.initiate_device_flow(scopes=self.scope)
                            print(f"[DEVICE_FLOW] Retry successful with basic scope: {self.scope}")
                        except Exception as retry_error:
                            print(f"[DEVICE_FLOW] Retry also failed: {retry_error}")
                            raise Exception(f"Failed to create device flow even with basic scope: {retry_error}")
                    else:
                        print(f"[DEVICE_FLOW] Device flow creation failed: {device_flow_error}")
                        raise Exception(f"Failed to create device flow: {device_flow_error}")

                # Show OAuth2 authentication dialog
                if progress_callback:
                    progress_callback("   Opening OAuth2 authentication dialog...")
                result = self._show_oauth2_dialog(flow, app, progress_callback)

            if "access_token" in result:
                # Save token cache immediately after successful authentication
                print("[CACHE] Saving token cache immediately after successful authentication...")
                self._save_token_cache_immediately(token_cache)
                return result['access_token']
            else:
                error_msg = result.get("error_description", result.get("error", "Unknown error"))
                raise Exception(f"Failed to acquire OAuth2 token: {error_msg}")

        except Exception as e:
            raise Exception(f"OAuth2 authentication failed: {str(e)}")

    def _show_oauth2_dialog(self, flow, app, progress_callback=None):
        """Show OAuth2 authentication dialog in GUI - THREAD SAFE"""
        import threading
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QThread

        print(f"[DIALOG] Creating OAuth2 dialog with code: {flow.get('user_code', 'N/A')}")
        print(f"[DIALOG] Verification URI: {flow.get('verification_uri', 'N/A')}")
        print(f"[DIALOG] Current thread: {threading.current_thread().name}")

        # Check if QApplication exists
        app_instance = QApplication.instance()
        if app_instance is None:
            print("[DIALOG] No QApplication instance found, using console fallback")
            return self._console_oauth2_fallback(flow, app)

        # Check if we're in the main thread
        main_thread = app_instance.thread()
        current_thread = QThread.currentThread()

        print(f"[DIALOG] Main thread: {main_thread}")
        print(f"[DIALOG] Current thread: {current_thread}")
        print(f"[DIALOG] Are we in main thread? {current_thread == main_thread}")

        if current_thread == main_thread:
            # We're in main thread, create dialog directly
            print("[DIALOG] In main thread, creating dialog directly")
            try:
                return self._create_oauth2_dialog_direct(flow, app, progress_callback)
            except Exception as e:
                print(f"[DIALOG] Direct dialog creation failed: {e}")
                print("[DIALOG] Falling back to console authentication")
                return self._console_oauth2_fallback(flow, app)
        else:
            # We're in worker thread, MUST use main thread execution
            print("[DIALOG] In worker thread, using thread-safe approach")
            return self._execute_dialog_in_main_thread(flow, app, progress_callback)

    def _execute_dialog_in_main_thread(self, flow, app, progress_callback=None):
        """Execute OAuth2 dialog in main thread using signals/slots - FIXED VERSION"""
        import time
        from PyQt6.QtCore import QObject, pyqtSignal, pyqtSlot
        from PyQt6.QtWidgets import QApplication

        print("[DIALOG] Setting up main thread execution...")

        # Create a result container that will be filled by main thread
        result_container = {
            'result': None,
            'error': None,
            'completed': False
        }

        # Create a signal emitter that lives in main thread
        class DialogExecutor(QObject):
            execute_signal = pyqtSignal()

            def __init__(self, oauth_handler, flow, app, progress_callback):
                super().__init__()
                self.oauth_handler = oauth_handler
                self.flow = flow
                self.app = app
                self.progress_callback = progress_callback
                # Connect signal to slot
                self.execute_signal.connect(self.execute_dialog, Qt.ConnectionType.QueuedConnection)

            @pyqtSlot()
            def execute_dialog(self):
                """This runs in main thread"""
                print("[DIALOG] Executing dialog in main thread via signal/slot")
                try:
                    result = self.oauth_handler._create_oauth2_dialog_direct(
                        self.flow, self.app, self.progress_callback
                    )
                    result_container['result'] = result
                    result_container['completed'] = True
                    print("[DIALOG] Dialog execution completed successfully in main thread")
                except Exception as e:
                    print(f"[DIALOG] Dialog execution failed in main thread: {e}")
                    import traceback
                    traceback.print_exc()
                    result_container['error'] = str(e)
                    result_container['completed'] = True

        # Get application instance
        app_instance = QApplication.instance()

        # Create executor and move to main thread
        executor = DialogExecutor(self, flow, app, progress_callback)
        executor.moveToThread(app_instance.thread())

        # Emit signal to execute dialog in main thread
        print("[DIALOG] Emitting signal to execute dialog in main thread...")
        executor.execute_signal.emit()

        # Wait for completion with timeout
        print("[DIALOG] Waiting for main thread execution to complete...")
        timeout = 300  # 5 minutes timeout
        start_time = time.time()

        while not result_container['completed'] and (time.time() - start_time) < timeout:
            time.sleep(0.05)  # Shorter sleep for better responsiveness
            # Process events to allow signal/slot execution
            app_instance.processEvents()

        # Cleanup executor
        try:
            executor.deleteLater()
            app_instance.processEvents()  # Process deletion
        except Exception as cleanup_error:
            print(f"[DIALOG] Executor cleanup warning: {cleanup_error}")

        # Check results
        if result_container['error']:
            print(f"[DIALOG] Main thread execution failed: {result_container['error']}")
            print("[DIALOG] Falling back to console authentication")
            return self._console_oauth2_fallback(flow, app)
        elif result_container['result']:
            print("[DIALOG] Main thread execution successful")
            return result_container['result']
        else:
            print("[DIALOG] Main thread execution timed out")
            print("[DIALOG] Falling back to console authentication")
            return self._console_oauth2_fallback(flow, app)

    def _create_oauth2_dialog_direct(self, flow, app, progress_callback=None):
        """Create OAuth2 dialog directly"""
        dialog = None
        try:
            print("[DIALOG] Starting direct dialog creation...")
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QTextEdit, QHBoxLayout, QApplication
            from PyQt6.QtCore import Qt
            from PyQt6.QtGui import QFont
            import webbrowser

            print("[DIALOG] PyQt6 imports successful")

            # Check if QApplication exists
            app_instance = QApplication.instance()
            if app_instance is None:
                print("[DIALOG] No QApplication instance - cannot create dialog")
                raise Exception("No QApplication instance available")

            print(f"[DIALOG] QApplication instance found: {app_instance}")

            # Ensure we're in the main thread for dialog creation
            import threading
            from PyQt6.QtCore import QThread
            current_thread = threading.current_thread()
            current_qt_thread = QThread.currentThread()
            main_qt_thread = app_instance.thread()

            print(f"[DIALOG] Creating dialog - Current Python thread: {current_thread.name}")
            print(f"[DIALOG] Current Qt thread: {current_qt_thread}")
            print(f"[DIALOG] Main Qt thread: {main_qt_thread}")
            print(f"[DIALOG] Is main thread: {current_qt_thread == main_qt_thread}")

            # Check if we're in the main Qt thread (this is the correct check)
            if current_qt_thread != main_qt_thread:
                print("[DIALOG] ERROR: Dialog creation attempted from non-main Qt thread!")
                raise Exception("Dialog must be created in main Qt thread")

            print("[DIALOG] ✅ Dialog creation in main Qt thread confirmed")

            # Create dialog with proper parent and cleanup
            print("[DIALOG] Creating QDialog...")
            dialog = QDialog()
            dialog.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, True)  # Auto-cleanup
            print("[DIALOG] QDialog created successfully")

            print("[DIALOG] Setting dialog properties...")
            dialog.setWindowTitle("OAuth2 Authentication Required")
            dialog.setFixedSize(600, 500)
            dialog.setModal(True)
            # Ensure dialog can receive focus and events
            dialog.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
            print("[DIALOG] Dialog properties set successfully")

            layout = QVBoxLayout(dialog)

            # Title
            title_label = QLabel("🔐 Microsoft Account Authentication")
            title_font = QFont()
            title_font.setPointSize(14)
            title_font.setBold(True)
            title_label.setFont(title_font)
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(title_label)

            # Instructions
            instructions = QLabel(
                "To continue, you need to authenticate with your Microsoft account.\n"
                "This is a one-time setup - future authentications will be automatic."
            )
            instructions.setWordWrap(True)
            instructions.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(instructions)

            # Code display with copy button
            code_label = QLabel("Your authentication code (click to copy):")
            layout.addWidget(code_label)

            code_layout = QHBoxLayout()
            code_display = QTextEdit()
            code_display.setPlainText(flow['user_code'])
            code_display.setMaximumHeight(40)
            code_display.setReadOnly(True)
            code_display.setAlignment(Qt.AlignmentFlag.AlignCenter)
            code_font = QFont("Courier", 16)
            code_font.setBold(True)
            code_display.setFont(code_font)
            code_layout.addWidget(code_display)

            copy_code_btn = QPushButton("Copy Code")
            def copy_code():
                print(f"[BUTTON] Copy Code clicked - copying: {flow['user_code']}")
                try:
                    # Simple immediate copy without threading or timers
                    if self._safe_copy_to_clipboard_simple(flow['user_code']):
                        copy_code_btn.setText("Copied!")
                    else:
                        copy_code_btn.setText("Copy Failed")
                except Exception as e:
                    print(f"[BUTTON] Copy code failed: {e}")
                    copy_code_btn.setText("Error")

            copy_code_btn.clicked.connect(copy_code)
            copy_code_btn.setMaximumWidth(100)
            code_layout.addWidget(copy_code_btn)
            layout.addLayout(code_layout)

            # URL display with copy button
            url_label = QLabel("Authentication website (click to copy):")
            layout.addWidget(url_label)

            url_layout = QHBoxLayout()
            url_display = QTextEdit()
            url_display.setPlainText(flow['verification_uri'])
            url_display.setMaximumHeight(40)
            url_display.setReadOnly(True)
            url_layout.addWidget(url_display)

            copy_url_btn = QPushButton("Copy URL")
            def copy_url():
                print(f"[BUTTON] Copy URL clicked - copying: {flow['verification_uri']}")
                try:
                    # Simple immediate copy without threading or timers
                    if self._safe_copy_to_clipboard_simple(flow['verification_uri']):
                        copy_url_btn.setText("Copied!")
                    else:
                        copy_url_btn.setText("Copy Failed")
                except Exception as e:
                    print(f"[BUTTON] Copy URL failed: {e}")
                    copy_url_btn.setText("Error")

            copy_url_btn.clicked.connect(copy_url)
            copy_url_btn.setMaximumWidth(100)
            url_layout.addWidget(copy_url_btn)
            layout.addLayout(url_layout)

            # Instructions
            steps_label = QLabel(
                "Steps:\n"
                "1. Click 'Open Browser' or copy the URL above\n"
                "2. Copy and enter the code shown above\n"
                "3. Sign in with your Microsoft account\n"
                "4. Click 'Continue' when done"
            )
            steps_label.setWordWrap(True)
            layout.addWidget(steps_label)

            # Status label
            status_label = QLabel("Waiting for authentication...")
            status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(status_label)

            # Buttons
            button_layout = QHBoxLayout()

            open_browser_btn = QPushButton("Open Browser")
            open_browser_btn.clicked.connect(lambda: webbrowser.open(flow['verification_uri']))
            button_layout.addWidget(open_browser_btn)

            continue_btn = QPushButton("Continue")
            def on_continue():
                print("[DIALOG] Continue button clicked!")
                dialog.accept()
            continue_btn.clicked.connect(on_continue)
            button_layout.addWidget(continue_btn)

            cancel_btn = QPushButton("Cancel")
            def on_cancel():
                print("[DIALOG] Cancel button clicked!")
                dialog.reject()
            cancel_btn.clicked.connect(on_cancel)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            print("[DIALOG] Dialog layout completed successfully")
            print("[DIALOG] Dialog is ready to be shown")

            # Show dialog and handle result
            print("[DIALOG] About to show dialog with exec()...")
            dialog_result = None
            try:
                # Ensure dialog is properly shown and focused
                dialog.show()
                dialog.activateWindow()
                dialog.raise_()

                # Process any pending events before exec
                app_instance.processEvents()

                # Execute dialog modally
                dialog_result = dialog.exec()
                print(f"[DIALOG] Dialog exec() completed with result: {dialog_result}")

            except Exception as exec_error:
                print(f"[DIALOG] Dialog exec() failed: {exec_error}")
                import traceback
                traceback.print_exc()
                raise
            finally:
                # Ensure dialog cleanup
                try:
                    if dialog is not None:
                        print("[DIALOG] Cleaning up dialog...")
                        dialog.hide()  # Hide first
                        app_instance.processEvents()  # Process hide events
                        dialog.close()
                        dialog.deleteLater()
                        # Process cleanup events
                        app_instance.processEvents()
                        print("[DIALOG] Dialog cleanup completed")
                except Exception as cleanup_error:
                    print(f"[DIALOG] Dialog cleanup warning: {cleanup_error}")

            if dialog_result == QDialog.DialogCode.Accepted:
                print("[DIALOG] User clicked Continue, proceeding with token acquisition...")
                # User clicked Continue, proceed with token acquisition in main thread
                if progress_callback:
                    progress_callback("   Completing OAuth2 authentication...")

                # Acquire token in main thread to avoid threading issues
                print("[DIALOG] Acquiring token by device flow...")
                result = app.acquire_token_by_device_flow(flow)
                print(f"[DIALOG] Token acquisition result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

                # Manually save the cache immediately after successful token acquisition
                if "access_token" in result and hasattr(app, 'token_cache') and app.token_cache:
                    try:
                        if app.token_cache.has_state_changed:
                            cache_filename = os.path.join(home, "oauth2_token_cache.bin")
                            with open(cache_filename, "w") as cache_file:
                                cache_file.write(app.token_cache.serialize())
                            print(f"[CACHE] Token cache manually saved after successful authentication")
                    except Exception as cache_error:
                        print(f"[CACHE] Warning: Failed to manually save cache: {cache_error}")

                if progress_callback:
                    if "access_token" in result:
                        progress_callback("   ✅ OAuth2 authentication completed successfully")
                        print("[DIALOG] OAuth2 authentication successful")
                    else:
                        progress_callback("   ❌ OAuth2 authentication failed")
                        print(f"[DIALOG] OAuth2 authentication failed: {result}")

                print("[DIALOG] OAuth2 dialog completed successfully")
                return result
            else:
                # User cancelled
                print("[DIALOG] User cancelled OAuth2 authentication")
                if progress_callback:
                    progress_callback("   ❌ OAuth2 authentication cancelled by user")
                print("[DIALOG] OAuth2 dialog cancelled")
                raise Exception("OAuth2 authentication cancelled by user")

        except ImportError:
            # Fallback to console if GUI not available
            return self._console_oauth2_fallback(flow, app)
        except Exception as e:
            print(f"[DIALOG] Dialog creation/execution failed: {e}")
            import traceback
            traceback.print_exc()
            # Ensure dialog cleanup even on error
            try:
                if dialog is not None:
                    dialog.close()
                    dialog.deleteLater()
            except:
                pass
            raise

    def _console_oauth2_fallback(self, flow, app):
        """Console fallback for OAuth2 authentication"""
        print(f"\n🔐 OAuth2 Authentication Required:")
        print(f"1. Go to: {flow['verification_uri']}")
        print(f"2. Enter code: {flow['user_code']}")
        print("3. Sign in with your Microsoft account")
        print("4. Return here and press Enter when done...")
        input("Press Enter after completing authentication...")

        result = app.acquire_token_by_device_flow(flow)

        # Manually save the cache immediately after successful token acquisition
        if "access_token" in result and hasattr(app, 'token_cache') and app.token_cache:
            try:
                if app.token_cache.has_state_changed:
                    cache_filename = os.path.join(home, "oauth2_token_cache.bin")
                    with open(cache_filename, "w") as cache_file:
                        cache_file.write(app.token_cache.serialize())
                    print(f"[CACHE] Token cache manually saved after console authentication")
            except Exception as cache_error:
                print(f"[CACHE] Warning: Failed to manually save cache: {cache_error}")

        return result

    def _safe_copy_to_clipboard(self, text, button, original_text):
        """Safely copy text to clipboard - SIMPLIFIED to avoid modal dialog conflicts"""
        import subprocess
        import sys
        from PyQt6.QtCore import QTimer

        print(f"[CLIPBOARD] Starting copy operation for: {text[:50]}{'...' if len(text) > 50 else ''}")

        try:
            # Disable button immediately
            button.setEnabled(False)
            button.setText("Copying...")

            # Simple clipboard operation - no complex logging or timers
            success = False

            if sys.platform == "win32":
                try:
                    # Use subprocess to copy to clipboard
                    process = subprocess.Popen(
                        ['clip'],
                        stdin=subprocess.PIPE,
                        text=True,
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        shell=False
                    )

                    # Send text with short timeout
                    try:
                        process.communicate(input=text, timeout=2)
                        success = (process.returncode == 0)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        success = False

                except Exception as e:
                    print(f"[CLIPBOARD] Subprocess failed: {e}")
                    success = False

            # Update button status
            if success:
                button.setText("Copied!")
                print(f"[CLIPBOARD] Successfully copied: {text[:30]}{'...' if len(text) > 30 else ''}")
            else:
                button.setText("Copy Failed")
                print(f"[CLIPBOARD] Failed to copy: {text[:30]}{'...' if len(text) > 30 else ''}")

            # Use QTimer to restore button state after a short delay to avoid painting conflicts
            def restore_button():
                try:
                    button.setText(original_text)
                    button.setEnabled(True)
                except Exception as restore_error:
                    print(f"[CLIPBOARD] Button restore error: {restore_error}")

            QTimer.singleShot(1000, restore_button)  # Restore after 1 second

        except Exception as e:
            print(f"[CLIPBOARD] Critical error: {e}")
            try:
                button.setText(original_text)
                button.setEnabled(True)
            except:
                pass  # If button operations fail, just continue

        print("[CLIPBOARD] Copy operation completed")

    def _safe_copy_to_clipboard_simple(self, text):
        """Simple clipboard copy without Qt interactions - for use in threads"""
        import subprocess
        import sys

        print(f"[CLIPBOARD] Simple copy operation for: {text[:50]}{'...' if len(text) > 50 else ''}")

        try:
            if sys.platform == "win32":
                # Use subprocess to copy to clipboard
                process = subprocess.Popen(
                    ['clip'],
                    stdin=subprocess.PIPE,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    shell=False
                )

                # Send text with short timeout
                try:
                    process.communicate(input=text, timeout=2)
                    success = (process.returncode == 0)
                    if success:
                        print(f"[CLIPBOARD] Simple copy successful: {text[:30]}{'...' if len(text) > 30 else ''}")
                    else:
                        print(f"[CLIPBOARD] Simple copy failed: {text[:30]}{'...' if len(text) > 30 else ''}")
                    return success
                except subprocess.TimeoutExpired:
                    process.kill()
                    print(f"[CLIPBOARD] Simple copy timeout: {text[:30]}{'...' if len(text) > 30 else ''}")
                    return False

        except Exception as e:
            print(f"[CLIPBOARD] Simple copy error: {e}")
            return False

    def create_xoauth2_string(self, email, access_token):
        """
        Create XOAUTH2 authentication string for SMTP
        Format: base64("user=" + userName + "^Aauth=Bearer " + accessToken + "^A^A")
        """
        auth_string = f"user={email}\x01auth=Bearer {access_token}\x01\x01"
        xoauth2_string = base64.b64encode(auth_string.encode()).decode()

        # Debug logging
        print(f"[XOAUTH2] Email: {email}")
        print(f"[XOAUTH2] Token length: {len(access_token)} characters")
        print(f"[XOAUTH2] Token starts with: {access_token[:20]}...")
        print(f"[XOAUTH2] Auth string length: {len(auth_string)} characters")
        print(f"[XOAUTH2] Base64 string length: {len(xoauth2_string)} characters")

        return xoauth2_string

    def _analyze_spam_error(self, error_message):
        """Analyze Microsoft spam filtering errors and provide guidance"""
        error_str = str(error_message).lower()

        print(f"[SPAM_ANALYSIS] Analyzing error: {error_message}")

        if "outboundspamexception" in error_str:
            print(f"[SPAM_ANALYSIS] ⚠️ Microsoft OutboundSpamException detected")

            if "wascl" in error_str:
                print(f"[SPAM_ANALYSIS] WASCL (Windows Azure Spam Confidence Level) triggered")

                if "suspend" in error_str:
                    print(f"[SPAM_ANALYSIS] Account sending is SUSPENDED")
                    print(f"[SPAM_ANALYSIS] Possible causes:")
                    print(f"[SPAM_ANALYSIS] - Exceeded daily sending limit (300 emails/day for personal accounts)")
                    print(f"[SPAM_ANALYSIS] - Sending too many emails too quickly")
                    print(f"[SPAM_ANALYSIS] - Email content triggered spam filters")
                    print(f"[SPAM_ANALYSIS] - Sending to too many unknown recipients")

                if "showtierupgrade" in error_str:
                    print(f"[SPAM_ANALYSIS] Microsoft suggests upgrading to paid service")
                    print(f"[SPAM_ANALYSIS] Consider Microsoft 365 for higher sending limits")

            print(f"[SPAM_ANALYSIS] Recommended actions:")
            print(f"[SPAM_ANALYSIS] 1. Wait 24 hours for automatic reset")
            print(f"[SPAM_ANALYSIS] 2. Reduce sending frequency")
            print(f"[SPAM_ANALYSIS] 3. Review email content for spam triggers")
            print(f"[SPAM_ANALYSIS] 4. Send to known recipients only")
            print(f"[SPAM_ANALYSIS] 5. Consider using Microsoft 365 business account")

            return True

        return False

    def _debug_token_info(self, access_token, email):
        """Debug token information to help troubleshoot authentication issues"""
        try:
            import base64
            import json

            print(f"[TOKEN_DEBUG] Analyzing token for {email}")
            print(f"[TOKEN_DEBUG] Token length: {len(access_token)} characters")
            print(f"[TOKEN_DEBUG] Token starts with: {access_token[:30]}...")
            print(f"[TOKEN_DEBUG] Token ends with: ...{access_token[-10:]}")

            # Split the token to check format
            parts = access_token.split('.')
            print(f"[TOKEN_DEBUG] Token has {len(parts)} parts (JWT should have 3)")

            if len(parts) == 3:
                print(f"[TOKEN_DEBUG] Token appears to be JWT format")
                # Decode header
                try:
                    header_part = parts[0]
                    padding = 4 - len(header_part) % 4
                    if padding != 4:
                        header_part += '=' * padding
                    header_bytes = base64.urlsafe_b64decode(header_part)
                    header = json.loads(header_bytes.decode('utf-8'))
                    print(f"[TOKEN_DEBUG] Token type: {header.get('typ', 'unknown')}")
                    print(f"[TOKEN_DEBUG] Algorithm: {header.get('alg', 'unknown')}")
                except Exception as e:
                    print(f"[TOKEN_DEBUG] Could not decode JWT header: {e}")

                # Decode payload
                try:
                    payload_part = parts[1]
                    padding = 4 - len(payload_part) % 4
                    if padding != 4:
                        payload_part += '=' * padding
                    payload_bytes = base64.urlsafe_b64decode(payload_part)
                    payload = json.loads(payload_bytes.decode('utf-8'))

                    print(f"[TOKEN_DEBUG] Audience: {payload.get('aud', 'unknown')}")
                    print(f"[TOKEN_DEBUG] Issuer: {payload.get('iss', 'unknown')}")
                    print(f"[TOKEN_DEBUG] Subject: {payload.get('sub', 'unknown')}")
                    print(f"[TOKEN_DEBUG] Scopes: {payload.get('scp', 'unknown')}")
                    print(f"[TOKEN_DEBUG] App ID: {payload.get('appid', 'unknown')}")

                    # Check expiration
                    import time
                    exp_time = payload.get('exp', 0)
                    current_time = int(time.time())
                    if exp_time:
                        time_left = exp_time - current_time
                        print(f"[TOKEN_DEBUG] Token expires in: {time_left} seconds")
                        if time_left <= 0:
                            print(f"[TOKEN_DEBUG] ⚠️ JWT TOKEN IS EXPIRED!")
                        else:
                            print(f"[TOKEN_DEBUG] ✅ JWT token is valid and not expired")
                    else:
                        print(f"[TOKEN_DEBUG] No expiration time found in JWT token")

                    # Check email/username
                    token_email = payload.get('email') or payload.get('preferred_username') or payload.get('upn')
                    if token_email:
                        print(f"[TOKEN_DEBUG] Token email: {token_email}")
                        if token_email.lower() != email.lower():
                            print(f"[TOKEN_DEBUG] ⚠️ EMAIL MISMATCH: token={token_email}, expected={email}")
                        else:
                            print(f"[TOKEN_DEBUG] ✅ Token email matches expected email")
                    else:
                        print(f"[TOKEN_DEBUG] No email found in JWT token")

                except Exception as e:
                    print(f"[TOKEN_DEBUG] Could not decode JWT payload: {e}")
            else:
                print(f"[TOKEN_DEBUG] Token is not JWT format - this is normal for Microsoft personal accounts")
                print(f"[TOKEN_DEBUG] Microsoft personal accounts often use opaque tokens")
                print(f"[TOKEN_DEBUG] Opaque tokens cannot be decoded but are still valid")

                # Basic opaque token analysis
                import re
                if re.match(r'^[A-Za-z0-9+/=_-]+$', access_token):
                    print(f"[TOKEN_DEBUG] ✅ Token contains valid base64-like characters")
                else:
                    print(f"[TOKEN_DEBUG] ⚠️ Token contains unusual characters")

                if 1000 <= len(access_token) <= 3000:
                    print(f"[TOKEN_DEBUG] ✅ Token length is typical for Microsoft access tokens")
                else:
                    print(f"[TOKEN_DEBUG] ⚠️ Token length is unusual for Microsoft access tokens")

        except Exception as e:
            print(f"[TOKEN_DEBUG] Error analyzing token: {e}")

    def is_microsoft_domain(self, email):
        """
        Check if email domain requires OAuth2 authentication
        """
        domain = email.split('@')[1].lower() if '@' in email else ''
        microsoft_domains = [
            'outlook.com', 'hotmail.com', 'live.com', 'msn.com',
            'outlook.office365.com', 'office365.com'
        ]
        return any(domain.endswith(ms_domain) for ms_domain in microsoft_domains)


# Global OAuth2 handler instance
oauth2_handler = OAuth2SMTPAuth()


def authenticate_smtp(smtp_connection, email, password, progress_callback=None):
    """
    Authenticate SMTP connection with OAuth2 support for Microsoft domains

    Args:
        smtp_connection: SMTP connection object
        email: Email address
        password: Password (for basic auth) or None (for OAuth2)
        progress_callback: Optional callback function for progress updates

    Returns:
        bool: True if authentication successful, False otherwise

    Raises:
        Exception: If authentication fails with detailed error message
    """
    try:
        # Check if this is a Microsoft domain that requires OAuth2
        if oauth2_handler.is_microsoft_domain(email):
            if progress_callback:
                progress_callback(f"   Detected Microsoft domain for {email}, attempting OAuth2 authentication...")

            try:
                # Configure OAuth2 if not already done
                oauth2_handler.configure_oauth2()

                # Get access token with email validation
                access_token = oauth2_handler.get_access_token(email, progress_callback)

                # Validate and debug the token
                oauth2_handler._debug_token_info(access_token, email)

                # Create XOAUTH2 string
                xoauth2_string = oauth2_handler.create_xoauth2_string(email, access_token)

                # Authenticate using XOAUTH2 - Microsoft requires two-step process
                print(f"[SMTP] Attempting XOAUTH2 authentication for {email}")
                print(f"[SMTP] XOAUTH2 string length: {len(xoauth2_string)} characters")

                # Ensure SMTP connection is properly initialized with EHLO
                # Note: EHLO must be sent after STARTTLS, so we always send it to be safe
                try:
                    print(f"[SMTP] Sending EHLO command to ensure connection is properly initialized")
                    code, response = smtp_connection.ehlo()
                    print(f"[SMTP] EHLO response: {code} {response}")
                    if code != 250:
                        raise Exception(f"EHLO failed: {code} {response}")
                    print(f"[SMTP] EHLO successful, connection ready for authentication")

                    # Debug: Show server capabilities
                    if hasattr(smtp_connection, 'ehlo_resp') and smtp_connection.ehlo_resp:
                        print(f"[SMTP] Server capabilities: {smtp_connection.ehlo_resp}")
                        # Check if XOAUTH2 is supported
                        if b'AUTH' in smtp_connection.ehlo_resp:
                            auth_line = [line for line in smtp_connection.ehlo_resp.split(b'\n') if b'AUTH' in line]
                            if auth_line:
                                print(f"[SMTP] Server AUTH capabilities: {auth_line[0]}")
                                if b'XOAUTH2' in auth_line[0]:
                                    print(f"[SMTP] ✅ Server supports XOAUTH2 authentication")
                                else:
                                    print(f"[SMTP] ⚠️ Server may not support XOAUTH2 authentication")

                except Exception as ehlo_error:
                    print(f"[SMTP] EHLO error: {ehlo_error}")
                    raise Exception(f"Failed to initialize SMTP connection: {ehlo_error}")

                # Step 1: Send AUTH XOAUTH2 command
                code, response = smtp_connection.docmd("AUTH", "XOAUTH2")
                print(f"[SMTP] AUTH XOAUTH2 response: {code} {response}")

                if code == 334:  # Server is ready for authentication data
                    # Step 2: Send the XOAUTH2 string
                    code, response = smtp_connection.docmd(xoauth2_string)
                    print(f"[SMTP] XOAUTH2 data response: {code} {response}")

                    if code == 235:  # Authentication successful
                        if progress_callback:
                            progress_callback(f"   ✅ OAuth2 authentication successful for {email}")
                        print(f"[SMTP] OAuth2 authentication successful for {email}")
                        return True
                    else:
                        # Check for specific spam-related errors
                        response_str = str(response)
                        if "OutboundSpamException" in response_str or "WASCL" in response_str:
                            print(f"[SMTP] ⚠️ Microsoft spam filter detected - this is not an OAuth2 issue")
                            print(f"[SMTP] Account may have sending restrictions or content triggered spam detection")
                        raise Exception(f"XOAUTH2 authentication failed: {code} {response}")
                else:
                    raise Exception(f"SMTP server doesn't support XOAUTH2 or unexpected response: {code} {response}")

            except Exception as oauth_error:
                oauth_error_msg = str(oauth_error)
                if progress_callback:
                    progress_callback(f"   ❌ OAuth2 authentication failed for {email}: {oauth_error_msg}")

                # Check if it's a configuration issue
                if "not properly configured" in oauth_error_msg or "MSAL library not available" in oauth_error_msg:
                    if progress_callback:
                        progress_callback(f"   💡 OAuth2 Configuration Help:")
                        progress_callback(f"   💡 1. Install MSAL: pip install msal")
                        progress_callback(f"   💡 2. Create oauth2_config.json with your Azure app credentials")
                        progress_callback(f"   💡 3. Or set environment variables: MICROSOFT_CLIENT_ID, MICROSOFT_CLIENT_SECRET, MICROSOFT_TENANT_ID")
                        progress_callback(f"   💡 4. Register your app in Azure AD with SMTP.Send permissions")

                # Try app password fallback if OAuth2 fails
                if password:
                    if progress_callback:
                        progress_callback(f"   ⚠️ Falling back to app password authentication for {email}...")
                    try:
                        smtp_connection.login(email, password)
                        if progress_callback:
                            progress_callback(f"   ✅ App password authentication successful for {email}")
                        return True
                    except Exception as app_pass_error:
                        if progress_callback:
                            progress_callback(f"   ❌ App password authentication also failed: {str(app_pass_error)}")
                        raise Exception(f"Both OAuth2 and app password authentication failed. OAuth2 error: {oauth_error_msg}. App password error: {str(app_pass_error)}")
                else:
                    raise Exception(f"OAuth2 authentication failed and no app password provided: {oauth_error_msg}")
        else:
            # Non-Microsoft domain, use basic authentication
            if progress_callback:
                progress_callback(f"   Using basic authentication for {email}")
            smtp_connection.login(email, password)
            if progress_callback:
                progress_callback(f"   ✅ Basic authentication successful for {email}")
            return True

    except Exception as e:
        error_msg = str(e)
        if progress_callback:
            progress_callback(f"   ❌ SMTP authentication failed for {email}: {error_msg}")
        raise


def authenticate_imap(imap_connection, email, password, progress_callback=None):
    """
    Authenticate IMAP connection with OAuth2 support for Microsoft domains

    Args:
        imap_connection: IMAP connection object
        email: Email address
        password: Password (for basic auth) or None (for OAuth2)
        progress_callback: Optional callback function for progress updates

    Returns:
        bool: True if authentication successful, False otherwise

    Raises:
        Exception: If authentication fails with detailed error message
    """
    try:
        # Check if this is a Microsoft domain that requires OAuth2
        if oauth2_handler.is_microsoft_domain(email):
            if progress_callback:
                progress_callback(f"   Detected Microsoft domain for {email}, attempting OAuth2 IMAP authentication...")

            try:
                # Configure OAuth2 if not already done
                oauth2_handler.configure_oauth2()

                # Get access token with email validation
                access_token = oauth2_handler.get_access_token(email, progress_callback)

                # Create XOAUTH2 string for IMAP
                xoauth2_string = oauth2_handler.create_xoauth2_string(email, access_token)

                # Authenticate using XOAUTH2 for IMAP
                imap_connection.authenticate('XOAUTH2', lambda x: xoauth2_string.encode())

                if progress_callback:
                    progress_callback(f"   ✅ OAuth2 IMAP authentication successful for {email}")
                return True

            except Exception as oauth_error:
                oauth_error_msg = str(oauth_error)
                if progress_callback:
                    progress_callback(f"   ❌ OAuth2 IMAP authentication failed for {email}: {oauth_error_msg}")

                # Try app password fallback if OAuth2 fails
                if password:
                    if progress_callback:
                        progress_callback(f"   ⚠️ Falling back to app password authentication for {email}...")
                    try:
                        imap_connection.login(email, password)
                        if progress_callback:
                            progress_callback(f"   ✅ App password IMAP authentication successful for {email}")
                        return True
                    except Exception as app_pass_error:
                        if progress_callback:
                            progress_callback(f"   ❌ App password IMAP authentication also failed: {str(app_pass_error)}")
                        raise Exception(f"Both OAuth2 and app password IMAP authentication failed. OAuth2 error: {oauth_error_msg}. App password error: {str(app_pass_error)}")
                else:
                    raise Exception(f"OAuth2 IMAP authentication failed and no app password provided: {oauth_error_msg}")
        else:
            # Non-Microsoft domain, use basic authentication
            if progress_callback:
                progress_callback(f"   Using basic IMAP authentication for {email}")
            imap_connection.login(email, password)
            if progress_callback:
                progress_callback(f"   ✅ Basic IMAP authentication successful for {email}")
            return True

    except Exception as e:
        error_msg = str(e)
        if progress_callback:
            progress_callback(f"   ❌ IMAP authentication failed for {email}: {error_msg}")
        raise


def get_smtp_server_for_domain(domain):
    """
    Get appropriate SMTP server for email domain
    """
    domain = domain.lower()

    if 'gmail' in domain:
        return 'smtp.gmail.com', 587
    elif any(ms_domain in domain for ms_domain in ['outlook', 'hotmail', 'live', 'msn']):
        return 'smtp.office365.com', 587  # Correct server for OAuth2 authentication
    elif 'yahoo' in domain:
        return 'smtp.mail.yahoo.com', 587
    elif 'gmx' in domain:
        return 'mail.gmx.com', 587
    else:
        # Default to Office365 for unknown domains
        return 'smtp.office365.com', 587



class HealthCheckWorker(QObject):
    finished = pyqtSignal()
    log_message = pyqtSignal(str)
    progress_update = pyqtSignal(str)

    def __init__(self, senders_file_name, failed_senders_file, use_vpn=False, emails_per_ip_change=5):
        super().__init__()
        self.senders_file_name = senders_file_name
        self.failed_senders_file = failed_senders_file
        self.cancel = False
        self.use_vpn = use_vpn
        self.emails_per_ip_change = emails_per_ip_change
        if self.use_vpn:
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from hma.simple_vpn import vpn_manager
                self.vpn_manager = vpn_manager
                self.vpn_manager.change_ip_after = self.emails_per_ip_change
                self.progress_update.emit("VPN manager initialized")
            except Exception as e:
                self.progress_update.emit(f"⚠️ Failed to initialize VPN manager: {str(e)}")
                self.use_vpn = False


class BounceCollectorWorker(QObject):
    finished = pyqtSignal()
    log_message = pyqtSignal(str)
    progress_update = pyqtSignal(str)
    bounce_count_update = pyqtSignal(int)

    def __init__(self, senders_file_name, bounced_emails_file, use_vpn=False, emails_per_ip_change=5, clean_senders=False):
        super().__init__()
        self.senders_file_name = senders_file_name
        self.bounced_emails_file = bounced_emails_file
        self.cancel = False
        self.use_vpn = use_vpn
        self.emails_per_ip_change = emails_per_ip_change
        self.clean_senders = clean_senders
        self.total_bounces = 0
        self.all_bounced_emails = set()  # Track all bounced emails for cleaning

        # Import VPN manager if needed
        if self.use_vpn:
            try:
                # Add the parent directory to sys.path
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from hma.simple_vpn import vpn_manager
                self.vpn_manager = vpn_manager
                # Configure VPN manager
                self.vpn_manager.change_ip_after = self.emails_per_ip_change
                self.progress_update.emit("VPN manager initialized")
            except Exception as e:
                self.progress_update.emit(f"⚠️ Failed to initialize VPN manager: {str(e)}")
                self.use_vpn = False

    def run(self):
        """Run the bounce collection in a separate thread"""
        # Send initial message
        self.progress_update.emit("Starting bounce email collection...\n")

        # Import required modules
        import sys
        import os
        import traceback

        try:
            # Initialize VPN if needed
            if self.use_vpn:
                try:
                    self.progress_update.emit("Initializing VPN connection...")

                    # Check if HMA VPN is installed
                    hma_path = r'C:\Program Files\Privax\HMA VPN\Vpn.exe'
                    if not os.path.exists(hma_path):
                        self.progress_update.emit(f"❌ HMA VPN not found at: {hma_path}")
                        self.use_vpn = False
                    else:
                        self.progress_update.emit(f"✓ HMA VPN found at: {hma_path}")

                        # Try to connect to VPN using GUI automation
                        self.progress_update.emit("Launching HMA VPN window and connecting automatically...")

                        try:
                            # Add the parent directory to sys.path
                            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                            from hma.turn_on import turn_on_vpn

                            # Call the GUI automation function to turn on VPN
                            if turn_on_vpn():
                                # Wait for VPN to fully establish connection
                                self.progress_update.emit("Waiting for VPN to establish connection...")
                                time.sleep(3)  # Wait 3 seconds for VPN to connect

                                # Get and display the current IP
                                try:
                                    current_ip = self.vpn_manager.get_current_ip()
                                    if current_ip:
                                        self.progress_update.emit(f"✅ VPN connected successfully (IP: {current_ip})")
                                    else:
                                        self.progress_update.emit("✅ VPN connected successfully (IP: unknown)")
                                except Exception as e:
                                    self.progress_update.emit("✅ VPN connected successfully (could not determine IP)")
                            else:
                                # If GUI automation fails, try the command-line approach as fallback
                                self.progress_update.emit("⚠️ GUI automation failed, trying command-line approach...")
                                if self.vpn_manager.ensure_vpn_on():
                                    # Get and display the current IP
                                    try:
                                        current_ip = self.vpn_manager.get_current_ip()
                                        if current_ip:
                                            self.progress_update.emit(f"✅ VPN connected successfully (IP: {current_ip})")
                                        else:
                                            self.progress_update.emit("✅ VPN connected successfully (IP: unknown)")
                                    except Exception as e:
                                        self.progress_update.emit("✅ VPN connected successfully (could not determine IP)")
                                else:
                                    self.progress_update.emit("❌ Failed to connect to VPN")
                                    self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
                                    self.use_vpn = False
                        except Exception as e:
                            import traceback
                            self.progress_update.emit(f"❌ Error during GUI automation: {str(e)}")
                            self.progress_update.emit(f"Error details: {traceback.format_exc()}")

                            # Try the command-line approach as fallback
                            self.progress_update.emit("⚠️ Trying command-line approach instead...")
                            if self.vpn_manager.ensure_vpn_on():
                                # Get and display the current IP
                                try:
                                    current_ip = self.vpn_manager.get_current_ip()
                                    if current_ip:
                                        self.progress_update.emit(f"✅ VPN connected successfully (IP: {current_ip})")
                                    else:
                                        self.progress_update.emit("✅ VPN connected successfully (IP: unknown)")
                                except Exception as e:
                                    self.progress_update.emit("✅ VPN connected successfully (could not determine IP)")
                            else:
                                self.progress_update.emit("❌ Failed to connect to VPN")
                                self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
                                self.use_vpn = False
                except Exception as e:
                    import traceback
                    self.progress_update.emit(f"❌ VPN error: {str(e)}")
                    self.progress_update.emit(f"Error details: {traceback.format_exc()}")
                    self.use_vpn = False

            # Load sender accounts
            self.progress_update.emit("Loading sender accounts...")
            senders_acc = []

            try:
                with open(self.senders_file_name, 'r') as senders_file:
                    for line in senders_file:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            senders_acc.append(line)

                self.progress_update.emit(f"Loaded {len(senders_acc)} sender accounts")

                if not senders_acc:
                    self.progress_update.emit("❌ No sender accounts found")
                    return

            except Exception as e:
                self.progress_update.emit(f"❌ Error loading senders file: {str(e)}")
                return

            # Process each sender account for bounce collection
            successful_checks = 0
            total_bounces_found = 0
            processed_accounts = 0

            for sender_info in senders_acc:
                if self.cancel:
                    break

                processed_accounts += 1
                self.progress_update.emit(f"\n--- Processing account {processed_accounts}/{len(senders_acc)} ---")

                # Change IP if using VPN and needed
                if self.use_vpn and processed_accounts > 1 and (processed_accounts - 1) % self.emails_per_ip_change == 0:
                    self.progress_update.emit(f"\n🔄 Changing IP address after {processed_accounts - 1} checks...")
                    try:
                        # Check if HMA VPN is still installed
                        hma_path = r'C:\Program Files\Privax\HMA VPN\Vpn.exe'
                        if not os.path.exists(hma_path):
                            self.progress_update.emit(f"❌ HMA VPN not found at: {hma_path}")
                            self.use_vpn = False
                        else:
                            # Get current IP before change
                            try:
                                old_ip = self.vpn_manager.get_current_ip()
                                if old_ip:
                                    self.progress_update.emit(f"Current IP before change: {old_ip}")
                            except Exception:
                                pass

                            # Try to change IP using GUI automation
                            try:
                                # Add the parent directory to sys.path if not already done
                                if os.path.dirname(os.path.dirname(os.path.abspath(__file__))) not in sys.path:
                                    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                                from hma.turn_on import turn_on_vpn
                                from hma.turn_off import turn_off_vpn

                                self.progress_update.emit("Disconnecting VPN to change IP...")
                                if turn_off_vpn():
                                    self.progress_update.emit("VPN disconnected, now reconnecting...")
                                    time.sleep(2)  # Wait a bit before reconnecting

                                    if turn_on_vpn():
                                        # Wait for VPN to fully establish connection
                                        self.progress_update.emit("Waiting for VPN to establish new connection...")
                                        time.sleep(3)  # Wait 3 seconds for VPN to connect

                                        # Get and display the new IP
                                        try:
                                            new_ip = self.vpn_manager.get_current_ip()
                                            if new_ip:
                                                if old_ip and old_ip != new_ip:
                                                    self.progress_update.emit(f"✅ IP address changed successfully from {old_ip} to {new_ip}")
                                                else:
                                                    self.progress_update.emit(f"✅ IP address changed to {new_ip}")
                                            else:
                                                self.progress_update.emit("✅ IP address changed successfully (new IP unknown)")
                                        except Exception:
                                            self.progress_update.emit("✅ IP address changed successfully (could not determine new IP)")
                                    else:
                                        self.progress_update.emit("❌ Failed to reconnect VPN after disconnecting")
                                        # Try the command-line approach as fallback
                                        self.progress_update.emit("⚠️ Trying command-line approach instead...")
                                        if self.vpn_manager.change_ip_if_needed(force=True):
                                            self.progress_update.emit("✅ IP address changed successfully via command-line")
                                        else:
                                            self.progress_update.emit("❌ Failed to change IP address")
                                else:
                                    self.progress_update.emit("❌ Failed to disconnect VPN for IP change")
                                    # Try the command-line approach as fallback
                                    self.progress_update.emit("⚠️ Trying command-line approach instead...")
                                    if self.vpn_manager.change_ip_if_needed(force=True):
                                        self.progress_update.emit("✅ IP address changed successfully via command-line")
                                    else:
                                        self.progress_update.emit("❌ Failed to change IP address")
                            except Exception as e:
                                import traceback
                                self.progress_update.emit(f"❌ Error during GUI automation for IP change: {str(e)}")
                                self.progress_update.emit(f"Error details: {traceback.format_exc()}")

                                # Try the command-line approach as fallback
                                self.progress_update.emit("⚠️ Trying command-line approach instead...")
                                if self.vpn_manager.change_ip_if_needed(force=True):
                                    # Get and display the new IP
                                    try:
                                        new_ip = self.vpn_manager.get_current_ip()
                                        if new_ip:
                                            if old_ip and old_ip != new_ip:
                                                self.progress_update.emit(f"✅ IP address changed successfully from {old_ip} to {new_ip}")
                                            else:
                                                self.progress_update.emit(f"✅ IP address changed to {new_ip}")
                                        else:
                                            self.progress_update.emit("✅ IP address changed successfully (new IP unknown)")
                                    except Exception:
                                        self.progress_update.emit("✅ IP address changed successfully (could not determine new IP)")
                                else:
                                    self.progress_update.emit("❌ Failed to change IP address")
                                    self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
                    except Exception as e:
                        import traceback
                        self.progress_update.emit(f"❌ Error changing IP: {str(e)}")
                        self.progress_update.emit(f"Error details: {traceback.format_exc()}")

                    self.progress_update.emit("Waiting for VPN to stabilize...")
                    time.sleep(2)  # Give the VPN time to stabilize

                try:
                    # Parse sender info
                    if ';' in sender_info:
                        sender, passwd = sender_info.split(';', 1)
                    elif ':' in sender_info:
                        sender, passwd = sender_info.split(':', 1)
                    else:
                        self.progress_update.emit(f"❌ Error: Invalid format for {sender_info}")
                        continue

                    sender = sender.strip()
                    passwd = passwd.strip()

                    self.progress_update.emit(f"🔍 Checking bounces for: {sender}")

                    # Collect bounces from this account
                    bounces = self.collect_bounces_from_account(sender, passwd)

                    if bounces is not None:
                        successful_checks += 1

                        # IMPORTANT: Remove the sender account itself from bounces list
                        # We should never mark a sender account as bounced
                        original_bounce_count = len(bounces)
                        bounces = [email for email in bounces if email.lower() != sender.lower()]
                        filtered_bounce_count = len(bounces)

                        if original_bounce_count > filtered_bounce_count:
                            self.progress_update.emit(f"   ⚠️ Filtered out sender account {sender} from bounce list (sender accounts should never be marked as bounced)")

                        bounce_count = filtered_bounce_count
                        total_bounces_found += bounce_count
                        self.total_bounces = total_bounces_found

                        if bounce_count > 0:
                            self.progress_update.emit(f"✅ {sender}: Found {bounce_count} bounced emails")
                            # Save bounces to file
                            self.save_bounced_emails(bounces)
                            # Track bounced emails for cleaning
                            self.all_bounced_emails.update(bounces)
                            self.bounce_count_update.emit(total_bounces_found)
                        else:
                            self.progress_update.emit(f"✅ {sender}: No bounces found")
                    else:
                        self.progress_update.emit(f"❌ {sender}: Failed to check bounces")

                except Exception as e:
                    self.progress_update.emit(f"❌ {sender}: Error - {str(e)}")

            # Final summary
            self.progress_update.emit(f"\n=== Bounce Collection Summary ===")
            self.progress_update.emit(f"Total accounts processed: {processed_accounts}")
            self.progress_update.emit(f"Successful checks: {successful_checks}")
            self.progress_update.emit(f"Total bounces found: {total_bounces_found}")
            self.bounce_count_update.emit(total_bounces_found)

            # Clean senders if requested
            if self.clean_senders and self.all_bounced_emails:
                self.progress_update.emit(f"\n=== Cleaning Senders File ===")
                self.progress_update.emit(f"Found {len(self.all_bounced_emails)} unique bounced email addresses")
                cleaned_count = self.clean_senders_file()
                if cleaned_count > 0:
                    self.progress_update.emit(f"✅ Removed {cleaned_count} bounced email addresses from senders file")
                else:
                    self.progress_update.emit(f"ℹ️ No bounced email addresses found in senders file to remove")
            elif self.clean_senders and not self.all_bounced_emails:
                self.progress_update.emit(f"\nℹ️ Clean senders was enabled but no bounced emails were found to remove")

        except Exception as e:
            self.progress_update.emit(f"Error during bounce collection: {str(e)}")

        # Turn off VPN if it was used
        if self.use_vpn:
            try:
                self.progress_update.emit("\nDisconnecting from VPN...")

                # Check if HMA VPN is still installed
                hma_path = r'C:\Program Files\Privax\HMA VPN\Vpn.exe'
                if not os.path.exists(hma_path):
                    self.progress_update.emit(f"❌ HMA VPN not found at: {hma_path}")
                else:
                    # Get current IP before disconnecting
                    try:
                        current_ip = self.vpn_manager.get_current_ip()
                        if current_ip:
                            self.progress_update.emit(f"Current IP before disconnecting: {current_ip}")
                    except Exception:
                        pass

                    # Try to disconnect using GUI automation
                    try:
                        # Add the parent directory to sys.path if not already done
                        if os.path.dirname(os.path.dirname(os.path.abspath(__file__))) not in sys.path:
                            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                        from hma.turn_off import turn_off_vpn

                        # Call the GUI automation function to turn off VPN
                        if turn_off_vpn():
                            self.progress_update.emit("✅ VPN disconnected successfully via GUI automation")

                            # Verify disconnection by checking new IP
                            try:
                                time.sleep(2)  # Wait for disconnection to take effect
                                new_ip = self.vpn_manager.get_current_ip()
                                if new_ip and current_ip and new_ip != current_ip:
                                    self.progress_update.emit(f"IP changed after disconnection: {new_ip}")
                            except Exception:
                                pass
                        else:
                            # If GUI automation fails, try the command-line approach as fallback
                            self.progress_update.emit("⚠️ GUI automation failed for disconnect, trying command-line approach...")
                            if self.vpn_manager.ensure_vpn_off():
                                self.progress_update.emit("✅ VPN disconnected successfully via command-line")

                                # Verify disconnection by checking new IP
                                try:
                                    time.sleep(2)  # Wait for disconnection to take effect
                                    new_ip = self.vpn_manager.get_current_ip()
                                    if new_ip and current_ip and new_ip != current_ip:
                                        self.progress_update.emit(f"IP changed after disconnection: {new_ip}")
                                except Exception:
                                    pass
                            else:
                                self.progress_update.emit("⚠️ Failed to disconnect from VPN")
                                self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
                    except Exception as e:
                        import traceback
                        self.progress_update.emit(f"❌ Error during GUI automation for disconnect: {str(e)}")
                        self.progress_update.emit(f"Error details: {traceback.format_exc()}")

                        # Try the command-line approach as fallback
                        self.progress_update.emit("⚠️ Trying command-line approach for disconnect...")
                        if self.vpn_manager.ensure_vpn_off():
                            self.progress_update.emit("✅ VPN disconnected successfully via command-line")

                            # Verify disconnection by checking new IP
                            try:
                                time.sleep(2)  # Wait for disconnection to take effect
                                new_ip = self.vpn_manager.get_current_ip()
                                if new_ip and current_ip and new_ip != current_ip:
                                    self.progress_update.emit(f"IP changed after disconnection: {new_ip}")
                            except Exception:
                                pass
                        else:
                            self.progress_update.emit("⚠️ Failed to disconnect from VPN")
                            self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
            except Exception as e:
                import traceback
                self.progress_update.emit(f"⚠️ VPN error during disconnect: {str(e)}")
                self.progress_update.emit(f"Error details: {traceback.format_exc()}")

        self.progress_update.emit("\nBounce collection completed.")
        self.finished.emit()

    def collect_bounces_from_account(self, email_address, password):
        """Collect bounce emails from a specific email account"""
        import imaplib
        import email as email_module
        import re

        bounces = []

        try:
            # Determine IMAP server based on email domain
            domain = email_address.split('@')[1].lower()

            if 'gmail' in domain:
                imap_server = 'imap.gmail.com'
                port = 993
            elif 'yahoo' in domain:
                imap_server = 'imap.mail.yahoo.com'
                port = 993
            elif 'outlook' in domain or 'hotmail' in domain or 'live' in domain:
                imap_server = 'outlook.office365.com'
                port = 993
            elif 'gmx' in domain:
                imap_server = 'imap.gmx.com'
                port = 993
            else:
                # Try common IMAP servers
                imap_server = f'imap.{domain}'
                port = 993

            self.progress_update.emit(f"   Connecting to {imap_server}...")

            # Connect to IMAP server with SSL retry logic
            mail = None
            max_retries = 3
            for retry in range(max_retries):
                try:
                    # Try different SSL contexts for better compatibility
                    if retry == 0:
                        # Standard SSL connection
                        mail = imaplib.IMAP4_SSL(imap_server, port, timeout=30)
                    elif retry == 1:
                        # Try with unverified SSL context
                        import ssl
                        context = ssl.create_default_context()
                        context.check_hostname = False
                        context.verify_mode = ssl.CERT_NONE
                        mail = imaplib.IMAP4_SSL(imap_server, port, ssl_context=context, timeout=30)
                    else:
                        # Try with TLS 1.2 specifically
                        import ssl
                        context = ssl.SSLContext(ssl.PROTOCOL_TLSv1_2)
                        context.check_hostname = False
                        context.verify_mode = ssl.CERT_NONE
                        mail = imaplib.IMAP4_SSL(imap_server, port, ssl_context=context, timeout=30)

                    # If connection successful, try to login with OAuth2 support
                    authenticate_imap(mail, email_address, password, self.progress_update.emit)
                    break  # Success, exit retry loop

                except Exception as e:
                    if retry < max_retries - 1:
                        self.progress_update.emit(f"   Connection attempt {retry + 1} failed, retrying... ({str(e)})")
                        time.sleep(2)  # Wait before retry
                        if mail:
                            try:
                                mail.close()
                            except:
                                pass
                        mail = None
                    else:
                        # Last SSL retry failed, try non-SSL as final fallback for port 143
                        if port == 993:
                            try:
                                self.progress_update.emit(f"   All SSL attempts failed, trying non-SSL IMAP on port 143...")
                                mail = imaplib.IMAP4(imap_server.replace('imap.', 'imap.'), 143, timeout=30)
                                mail.starttls()  # Try to upgrade to TLS
                                authenticate_imap(mail, email_address, password, self.progress_update.emit)
                                self.progress_update.emit(f"   ✅ Connected via non-SSL IMAP with STARTTLS")
                                break
                            except Exception as e2:
                                self.progress_update.emit(f"   Non-SSL fallback also failed: {str(e2)}")
                                # Re-raise the original SSL exception
                                raise e
                        else:
                            # Re-raise the exception
                            raise

            # Select inbox
            mail.select('inbox')

            # Search for bounce/delivery failure emails
            # Look for common bounce indicators
            bounce_subjects = [
                'MAILER-DAEMON',
                'Mail delivery failed',
                'Undelivered Mail Returned',
                'Delivery Status Notification',
                'Mail Delivery Subsystem',
                'Returned mail',
                'Undeliverable',
                'Mail System Error',
                'Delivery failure',
                'Message not delivered',
                'returning message to sender',
                'Mail delivery failed: returning message to sender',
                'Delivery Status Notification (Failure)',
                'Undelivered Mail',
                'Mail Delivery Failed',
                'Permanent Delivery Failure',
                'Message could not be delivered',
                'Delivery has failed',
                'Mail Delivery Error',
                'Bounce',
                'NDR',
                'Non-Delivery Report',
                'created automatically by mail delivery software',
                'could not be delivered',
                'permanent error',
                'mailbox unavailable',
                'action not taken'
            ]

            # Search for bounce emails from the last 30 days
            import datetime
            since_date = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%d-%b-%Y")

            all_bounce_uids = set()

            for subject_pattern in bounce_subjects:
                try:
                    # Search by subject
                    result, data = mail.search(None, f'(SINCE "{since_date}" SUBJECT "{subject_pattern}")')
                    if result == 'OK' and data[0]:
                        uids = data[0].split()
                        all_bounce_uids.update(uids)
                except Exception as e:
                    continue

            # Also search by sender patterns
            bounce_senders = [
                'MAILER-DAEMON',
                'postmaster',
                'noreply',
                'Mail Delivery Subsystem',
                'Mail Delivery System',
                'Delivery Status',
                'System Administrator',
                'Automatic Reply',
                'bounce',
                'daemon',
                'no-reply'
            ]

            for sender_pattern in bounce_senders:
                try:
                    result, data = mail.search(None, f'(SINCE "{since_date}" FROM "{sender_pattern}")')
                    if result == 'OK' and data[0]:
                        uids = data[0].split()
                        all_bounce_uids.update(uids)
                except Exception as e:
                    continue

            # Also search for emails containing bounce keywords in the body
            bounce_body_keywords = [
                'mailbox unavailable',
                'permanent error',
                'could not be delivered',
                'SMTP error',
                'action not taken',
                'delivery software'
            ]

            for keyword in bounce_body_keywords:
                try:
                    result, data = mail.search(None, f'(SINCE "{since_date}" BODY "{keyword}")')
                    if result == 'OK' and data[0]:
                        uids = data[0].split()
                        all_bounce_uids.update(uids)
                except Exception as e:
                    continue

            self.progress_update.emit(f"   Found {len(all_bounce_uids)} potential bounce emails")

            # Process each bounce email
            for uid in list(all_bounce_uids)[:50]:  # Limit to 50 most recent
                if self.cancel:
                    break

                try:
                    result, data = mail.fetch(uid, '(RFC822)')
                    if result == 'OK':
                        email_body = data[0][1]
                        email_message = email_module.message_from_bytes(email_body)

                        # Extract bounced email addresses (pass sender account to exclude it)
                        bounced_emails = self.extract_bounced_emails(email_message, email_address)
                        if bounced_emails:
                            self.progress_update.emit(f"   Found bounced emails: {', '.join(bounced_emails)}")
                        bounces.extend(bounced_emails)

                except Exception as e:
                    continue

            mail.close()
            mail.logout()

            # Remove duplicates
            bounces = list(set(bounces))
            return bounces

        except Exception as e:
            error_msg = str(e)
            if "SSL" in error_msg or "ssl" in error_msg.lower():
                self.progress_update.emit(f"   ❌ IMAP SSL error: {error_msg}")
                self.progress_update.emit(f"   💡 This is often caused by firewall/antivirus blocking SSL connections")
                self.progress_update.emit(f"   💡 Try temporarily disabling antivirus or adding Python to firewall exceptions")
            elif "authentication" in error_msg.lower() or "login" in error_msg.lower():
                self.progress_update.emit(f"   ❌ IMAP authentication error: {error_msg}")
                self.progress_update.emit(f"   💡 Check if the email account has IMAP enabled and credentials are correct")
            else:
                self.progress_update.emit(f"   ❌ IMAP error: {error_msg}")
            return None

    def extract_bounced_emails(self, email_message, sender_account=None):
        """Extract bounced email addresses from a bounce message"""
        import re

        bounced_emails = []

        try:
            # Get email content
            content = ""
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain":
                        content += part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                content = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')

            # Debug: Show a portion of the content being processed
            content_preview = content[:500].replace('\n', ' ').replace('\r', ' ')
            self.progress_update.emit(f"   Processing bounce content: {content_preview}...")

            # Common patterns for bounced email addresses
            patterns = [
                r'(?:to|TO|To):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'(?:recipient|RECIPIENT|Recipient):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'(?:address|ADDRESS|Address):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'(?:user|USER|User):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})>',
                r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}).*(?:not found|unknown|invalid|failed|bounced|undeliverable)',
                r'(?:Original-Recipient|original-recipient):\s*rfc822;\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'(?:Final-Recipient|final-recipient):\s*rfc822;\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'(?:X-Failed-Recipients|x-failed-recipients):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'(?:The following address|The following addresses).*?([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}).*(?:does not exist|doesn\'t exist|is not valid|was rejected)',
                r'(?:could not be delivered to|couldn\'t be delivered to).*?([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'(?:returning message to sender).*?([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                # Pattern for "The following address failed:" format
                r'(?:The following address failed|following address failed):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                # Pattern for email followed by colon and error (like <EMAIL>:)
                r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}):\s*(?:SMTP|smtp|Error|error|550|5\d\d)',
                # Pattern for email followed by colon and any text
                r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}):\s*(?:.*(?:mailbox|unavailable|not|found|rejected|failed|error))',
                # More general pattern for emails in bounce messages
                r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(?=\s*:?\s*(?:SMTP|smtp|Error|error|5\d\d|mailbox|unavailable|rejected|failed))'
            ]

            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    self.progress_update.emit(f"   Pattern {i+1} matched: {matches}")
                bounced_emails.extend(matches)

            # Filter out sender addresses and system addresses
            filtered_bounces = []
            for email_addr in bounced_emails:
                email_addr = email_addr.strip().lower()
                if email_addr and '@' in email_addr:
                    # Skip system addresses
                    if not any(skip in email_addr for skip in ['mailer-daemon', 'postmaster', 'noreply', 'no-reply']):
                        # IMPORTANT: Never include the sender account itself as a bounced email
                        if sender_account and email_addr == sender_account.lower():
                            continue  # Skip the sender account
                        filtered_bounces.append(email_addr)

            return list(set(filtered_bounces))  # Remove duplicates

        except Exception as e:
            return []

    def save_bounced_emails(self, bounced_emails):
        """Save bounced emails to file, avoiding duplicates"""
        import datetime
        import os

        try:
            # Create file if it doesn't exist
            if not os.path.exists(self.bounced_emails_file):
                with open(self.bounced_emails_file, 'w', encoding='utf-8') as file:
                    file.write("# Bounced emails log\n")
                    file.write("# Format: Timestamp | Bounced Email\n\n")

            # Read existing entries to avoid duplicates
            existing_emails = set()
            try:
                with open(self.bounced_emails_file, 'r', encoding='utf-8') as file:
                    for line in file:
                        if line.startswith('#') or not line.strip():
                            continue
                        parts = line.split(' | ', 1)
                        if len(parts) >= 2:
                            existing_emails.add(parts[1].strip())
            except Exception:
                pass

            # Add new bounced emails
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            new_entries = []

            for email_addr in bounced_emails:
                if email_addr not in existing_emails:
                    new_entries.append(f"{timestamp} | {email_addr}\n")
                    existing_emails.add(email_addr)

            if new_entries:
                with open(self.bounced_emails_file, 'a', encoding='utf-8') as file:
                    file.writelines(new_entries)

        except Exception as e:
            self.progress_update.emit(f"Error saving bounced emails: {str(e)}")

    def clean_senders_file(self):
        """Remove bounced email addresses from the senders file"""
        try:
            cleaned_count = 0
            sender_accounts = set()

            # Read current senders file
            with open(self.senders_file_name, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            # First pass: collect all sender account emails to protect them
            for line in lines:
                line_stripped = line.strip()
                if not line_stripped or line_stripped.startswith('#'):
                    continue

                # Extract email from sender line (format: email:password or email;password)
                if ':' in line_stripped:
                    sender_email = line_stripped.split(':')[0].strip().lower()
                    sender_accounts.add(sender_email)
                elif ';' in line_stripped:
                    sender_email = line_stripped.split(';')[0].strip().lower()
                    sender_accounts.add(sender_email)

            # Second pass: filter out lines containing bounced emails (but NEVER sender accounts)
            cleaned_lines = []
            for line in lines:
                line_stripped = line.strip()
                if not line_stripped or line_stripped.startswith('#'):
                    # Keep comments and empty lines
                    cleaned_lines.append(line)
                    continue

                # Extract email from sender line (format: email:password or email;password)
                sender_email = None
                if ':' in line_stripped:
                    sender_email = line_stripped.split(':')[0].strip().lower()
                elif ';' in line_stripped:
                    sender_email = line_stripped.split(';')[0].strip().lower()

                # IMPORTANT: Never remove sender accounts, even if they appear in bounce list
                if sender_email and sender_email in sender_accounts:
                    # This is a sender account - always keep it
                    cleaned_lines.append(line)
                    if sender_email in [email.lower() for email in self.all_bounced_emails]:
                        self.progress_update.emit(f"   ⚠️ Protecting sender account {sender_email} from removal (sender accounts should never be cleaned)")
                    continue

                # Check if this email is in the bounced emails list (only for non-sender accounts)
                if sender_email and sender_email in [email.lower() for email in self.all_bounced_emails]:
                    self.progress_update.emit(f"   Removing bounced email: {sender_email}")
                    cleaned_count += 1
                else:
                    cleaned_lines.append(line)

            # Write back the cleaned file
            if cleaned_count > 0:
                with open(self.senders_file_name, 'w', encoding='utf-8') as file:
                    file.writelines(cleaned_lines)

            return cleaned_count

        except Exception as e:
            self.progress_update.emit(f"Error cleaning senders file: {str(e)}")
            return 0

    def cancel_check(self):
        """Cancel the bounce collection"""
        self.cancel = True


class HealthCheckWorker(QObject):
    finished = pyqtSignal()
    log_message = pyqtSignal(str)
    progress_update = pyqtSignal(str)

    def __init__(self, senders_file_name, failed_senders_file, use_vpn=False, emails_per_ip_change=5):
        super().__init__()
        self.senders_file_name = senders_file_name
        self.failed_senders_file = failed_senders_file
        self.cancel = False
        self.use_vpn = use_vpn
        self.emails_per_ip_change = emails_per_ip_change

        # Import VPN manager if needed
        if self.use_vpn:
            try:
                import sys
                import os
                # Add the parent directory to sys.path
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from hma.simple_vpn import vpn_manager
                self.vpn_manager = vpn_manager
                # Configure VPN manager
                self.vpn_manager.change_ip_after = self.emails_per_ip_change
                self.progress_update.emit("VPN manager initialized")
            except Exception as e:
                self.progress_update.emit(f"⚠️ Failed to initialize VPN manager: {str(e)}")
                self.use_vpn = False

    def run(self):
        """Run the health check in a separate thread"""
        # Send initial message
        self.progress_update.emit("Starting sender health check...\n")

        # Import required modules
        import sys
        import os
        import traceback

        # Initialize VPN if needed
        if self.use_vpn:
            try:
                self.progress_update.emit("Initializing VPN connection...")

                # Check if HMA VPN is installed
                hma_path = r'C:\Program Files\Privax\HMA VPN\Vpn.exe'
                if not os.path.exists(hma_path):
                    self.progress_update.emit(f"❌ HMA VPN not found at: {hma_path}")
                    self.use_vpn = False
                else:
                    self.progress_update.emit(f"✓ HMA VPN found at: {hma_path}")

                    # Try to connect to VPN using GUI automation
                    self.progress_update.emit("Launching HMA VPN window and connecting automatically...")

                    try:
                        # Add the parent directory to sys.path
                        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                        from hma.turn_on import turn_on_vpn

                        # Call the GUI automation function to turn on VPN
                        if turn_on_vpn():
                            # Get and display the current IP
                            try:
                                current_ip = self.vpn_manager.get_current_ip()
                                if current_ip:
                                    self.progress_update.emit(f"✅ VPN connected successfully (IP: {current_ip})")
                                else:
                                    self.progress_update.emit("✅ VPN connected successfully (IP: unknown)")
                            except Exception as e:
                                self.progress_update.emit("✅ VPN connected successfully (could not determine IP)")
                        else:
                            # If GUI automation fails, try the command-line approach as fallback
                            self.progress_update.emit("⚠️ GUI automation failed, trying command-line approach...")
                            if self.vpn_manager.ensure_vpn_on():
                                # Get and display the current IP
                                try:
                                    current_ip = self.vpn_manager.get_current_ip()
                                    if current_ip:
                                        self.progress_update.emit(f"✅ VPN connected successfully (IP: {current_ip})")
                                    else:
                                        self.progress_update.emit("✅ VPN connected successfully (IP: unknown)")
                                except Exception as e:
                                    self.progress_update.emit("✅ VPN connected successfully (could not determine IP)")
                            else:
                                self.progress_update.emit("❌ Failed to connect to VPN")
                                self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
                                self.use_vpn = False
                    except Exception as e:
                        import traceback
                        self.progress_update.emit(f"❌ Error during GUI automation: {str(e)}")
                        self.progress_update.emit(f"Error details: {traceback.format_exc()}")

                        # Try the command-line approach as fallback
                        self.progress_update.emit("⚠️ Trying command-line approach instead...")
                        if self.vpn_manager.ensure_vpn_on():
                            # Get and display the current IP
                            try:
                                current_ip = self.vpn_manager.get_current_ip()
                                if current_ip:
                                    self.progress_update.emit(f"✅ VPN connected successfully (IP: {current_ip})")
                                else:
                                    self.progress_update.emit("✅ VPN connected successfully (IP: unknown)")
                            except Exception as e:
                                self.progress_update.emit("✅ VPN connected successfully (could not determine IP)")
                        else:
                            self.progress_update.emit("❌ Failed to connect to VPN")
                            self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
                            self.use_vpn = False
            except Exception as e:
                import traceback
                self.progress_update.emit(f"❌ VPN error: {str(e)}")
                self.progress_update.emit(f"Error details: {traceback.format_exc()}")
                self.use_vpn = False

        # Load senders from file
        try:
            with open(self.senders_file_name, 'r') as senders_file:
                senders = senders_file.readlines()

            # Group senders by ISP
            gmail_senders = []
            yahoo_senders = []
            gmx_senders = []
            other_senders = []

            for sender in senders:
                sender = sender.strip()
                if not sender:
                    continue

                if "@gmail.com" in sender:
                    gmail_senders.append(sender)
                elif "@yahoo.com" in sender:
                    yahoo_senders.append(sender)
                elif "@gmx.com" in sender or "@gmx.de" in sender or "@gmx.us" in sender:
                    gmx_senders.append(sender)
                else:
                    other_senders.append(sender)

            # Test each group
            total_senders = 0
            successful_senders = 0
            checked_since_ip_change = 0

            def test_sender_group(senders, smtp_host, group_name):
                nonlocal total_senders, successful_senders, checked_since_ip_change

                if not senders:
                    self.progress_update.emit(f"No {group_name} senders found.")
                    return

                self.progress_update.emit(f"\nTesting {len(senders)} {group_name} senders...")

                for i, sender_info in enumerate(senders):
                    if self.cancel:
                        self.progress_update.emit("Health check cancelled.")
                        return

                    # Check if we need to change IP (only for GMX senders when using VPN)
                    if self.use_vpn and group_name.lower() == "gmx":
                        checked_since_ip_change += 1
                        self.progress_update.emit(f"GMX sender check count: {checked_since_ip_change}/{self.emails_per_ip_change}")

                        if checked_since_ip_change >= self.emails_per_ip_change:
                            self.progress_update.emit(f"\n🔄 Changing IP address after {checked_since_ip_change} checks...")
                            try:
                                # Check if HMA VPN is still installed
                                hma_path = r'C:\Program Files\Privax\HMA VPN\Vpn.exe'
                                if not os.path.exists(hma_path):
                                    self.progress_update.emit(f"❌ HMA VPN not found at: {hma_path}")
                                    self.use_vpn = False
                                else:
                                    # Get current IP before change
                                    try:
                                        old_ip = self.vpn_manager.get_current_ip()
                                        if old_ip:
                                            self.progress_update.emit(f"Current IP before change: {old_ip}")
                                    except Exception:
                                        pass

                                    # Try to change IP using GUI automation
                                    try:
                                        # Add the parent directory to sys.path if not already done
                                        if os.path.dirname(os.path.dirname(os.path.abspath(__file__))) not in sys.path:
                                            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                                        from hma.turn_on import turn_on_vpn

                                        # First disconnect and then connect again to change IP
                                        from hma.turn_off import turn_off_vpn

                                        self.progress_update.emit("Disconnecting VPN to change IP...")
                                        if turn_off_vpn():
                                            self.progress_update.emit("VPN disconnected, now reconnecting...")
                                            time.sleep(2)  # Wait a bit before reconnecting

                                            if turn_on_vpn():
                                                # Get and display the new IP
                                                try:
                                                    new_ip = self.vpn_manager.get_current_ip()
                                                    if new_ip:
                                                        if old_ip and old_ip != new_ip:
                                                            self.progress_update.emit(f"✅ IP address changed successfully from {old_ip} to {new_ip}")
                                                        else:
                                                            self.progress_update.emit(f"✅ IP address changed to {new_ip}")
                                                    else:
                                                        self.progress_update.emit("✅ IP address changed successfully (new IP unknown)")
                                                except Exception:
                                                    self.progress_update.emit("✅ IP address changed successfully (could not determine new IP)")
                                            else:
                                                self.progress_update.emit("❌ Failed to reconnect VPN after disconnecting")
                                                # Try the command-line approach as fallback
                                                self.progress_update.emit("⚠️ Trying command-line approach instead...")
                                                if self.vpn_manager.change_ip_if_needed(force=True):
                                                    self.progress_update.emit("✅ IP address changed successfully via command-line")
                                                else:
                                                    self.progress_update.emit("❌ Failed to change IP address")
                                        else:
                                            self.progress_update.emit("❌ Failed to disconnect VPN for IP change")
                                            # Try the command-line approach as fallback
                                            self.progress_update.emit("⚠️ Trying command-line approach instead...")
                                            if self.vpn_manager.change_ip_if_needed(force=True):
                                                self.progress_update.emit("✅ IP address changed successfully via command-line")
                                            else:
                                                self.progress_update.emit("❌ Failed to change IP address")
                                    except Exception as e:
                                        import traceback
                                        self.progress_update.emit(f"❌ Error during GUI automation for IP change: {str(e)}")
                                        self.progress_update.emit(f"Error details: {traceback.format_exc()}")

                                        # Try the command-line approach as fallback
                                        self.progress_update.emit("⚠️ Trying command-line approach instead...")
                                        if self.vpn_manager.change_ip_if_needed(force=True):
                                            # Get and display the new IP
                                            try:
                                                new_ip = self.vpn_manager.get_current_ip()
                                                if new_ip:
                                                    if old_ip and old_ip != new_ip:
                                                        self.progress_update.emit(f"✅ IP address changed successfully from {old_ip} to {new_ip}")
                                                    else:
                                                        self.progress_update.emit(f"✅ IP address changed to {new_ip}")
                                                else:
                                                    self.progress_update.emit("✅ IP address changed successfully (new IP unknown)")
                                            except Exception:
                                                self.progress_update.emit("✅ IP address changed successfully (could not determine new IP)")
                                        else:
                                            self.progress_update.emit("❌ Failed to change IP address")
                                            self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
                            except Exception as e:
                                import traceback
                                self.progress_update.emit(f"❌ Error changing IP: {str(e)}")
                                self.progress_update.emit(f"Error details: {traceback.format_exc()}")

                            checked_since_ip_change = 0
                            self.progress_update.emit("Waiting for VPN to stabilize...")
                            time.sleep(2)  # Give the VPN time to stabilize

                    # Add a small delay between checks to avoid rate limiting
                    # But not for the first sender in each group
                    if i > 0:
                        time.sleep(1)  # 1 second delay between checks

                    total_senders += 1

                    try:
                        # Parse sender info
                        if ';' in sender_info:
                            sender, passwd = sender_info.split(';')
                        elif ':' in sender_info:
                            sender, passwd = sender_info.split(':')
                        else:
                            self.progress_update.emit(f"❌ Error: Invalid format for {sender_info}")
                            continue

                        # Test SMTP login with retry logic
                        import smtplib
                        import ssl
                        import time
                        import socket

                        # Increase timeout and add retry logic
                        max_retries = 2
                        timeout_value = 30  # Increased from 10 to 30 seconds

                        for retry in range(max_retries + 1):
                            if self.cancel:
                                return

                            try:
                                context = ssl.create_default_context()
                                with smtplib.SMTP(smtp_host, port=587, timeout=timeout_value) as smtp:
                                    smtp.starttls(context=context)
                                    # Use new OAuth2-aware authentication
                                    authenticate_smtp(smtp, sender, passwd, self.progress_update.emit)
                                    self.progress_update.emit(f"✅ {sender}: Login successful")
                                    successful_senders += 1
                                    break  # Success, exit retry loop
                            except (smtplib.SMTPServerDisconnected, TimeoutError, ConnectionRefusedError, socket.timeout) as e:
                                # Only retry on timeout/connection errors
                                if retry < max_retries:
                                    self.progress_update.emit(f"⚠️ {sender}: Retry {retry+1}/{max_retries} - {str(e)}")
                                    time.sleep(2)  # Wait before retrying
                                else:
                                    # Last retry failed
                                    raise  # Re-raise to be caught by outer exception handler
                            except Exception as e:
                                # Don't retry other errors
                                raise

                    except Exception as e:
                        error_msg = f"Health check: {str(e)}"
                        self.progress_update.emit(f"❌ {sender}: Login failed - {str(e)}")

                        # Save to failed senders file
                        self.save_failed_sender(sender, error_msg)

            # Test each group with appropriate SMTP server
            test_sender_group(gmail_senders, "smtp.gmail.com", "Gmail")
            test_sender_group(gmx_senders, "mail.gmx.com", "GMX")
            test_sender_group(yahoo_senders, "smtp.mail.yahoo.com", "Yahoo")
            test_sender_group(other_senders, "smtp.office365.com", "Other")

            # Show summary
            self.progress_update.emit(f"\n--- Health Check Complete ---")
            self.progress_update.emit(f"Total senders tested: {total_senders}")
            self.progress_update.emit(f"Successful logins: {successful_senders}")
            self.progress_update.emit(f"Failed logins: {total_senders - successful_senders}")

            if total_senders > 0:
                success_rate = (successful_senders / total_senders) * 100
                self.progress_update.emit(f"Success rate: {success_rate:.1f}%")

        except Exception as e:
            self.progress_update.emit(f"Error during health check: {str(e)}")

        # Turn off VPN if it was used
        if self.use_vpn:
            try:
                self.progress_update.emit("\nDisconnecting from VPN...")

                # Check if HMA VPN is still installed
                hma_path = r'C:\Program Files\Privax\HMA VPN\Vpn.exe'
                if not os.path.exists(hma_path):
                    self.progress_update.emit(f"❌ HMA VPN not found at: {hma_path}")
                else:
                    # Get current IP before disconnecting
                    try:
                        current_ip = self.vpn_manager.get_current_ip()
                        if current_ip:
                            self.progress_update.emit(f"Current IP before disconnecting: {current_ip}")
                    except Exception:
                        pass

                    # Try to disconnect using GUI automation
                    try:
                        # Add the parent directory to sys.path if not already done
                        if os.path.dirname(os.path.dirname(os.path.abspath(__file__))) not in sys.path:
                            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                        from hma.turn_off import turn_off_vpn

                        # Call the GUI automation function to turn off VPN
                        if turn_off_vpn():
                            self.progress_update.emit("✅ VPN disconnected successfully via GUI automation")

                            # Verify disconnection by checking new IP
                            try:
                                time.sleep(2)  # Wait for disconnection to take effect
                                new_ip = self.vpn_manager.get_current_ip()
                                if new_ip and current_ip and new_ip != current_ip:
                                    self.progress_update.emit(f"IP changed after disconnection: {new_ip}")
                            except Exception:
                                pass
                        else:
                            # If GUI automation fails, try the command-line approach as fallback
                            self.progress_update.emit("⚠️ GUI automation failed for disconnect, trying command-line approach...")
                            if self.vpn_manager.ensure_vpn_off():
                                self.progress_update.emit("✅ VPN disconnected successfully via command-line")

                                # Verify disconnection by checking new IP
                                try:
                                    time.sleep(2)  # Wait for disconnection to take effect
                                    new_ip = self.vpn_manager.get_current_ip()
                                    if new_ip and current_ip and new_ip != current_ip:
                                        self.progress_update.emit(f"IP changed after disconnection: {new_ip}")
                                except Exception:
                                    pass
                            else:
                                self.progress_update.emit("⚠️ Failed to disconnect from VPN")
                                self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
                    except Exception as e:
                        import traceback
                        self.progress_update.emit(f"❌ Error during GUI automation for disconnect: {str(e)}")
                        self.progress_update.emit(f"Error details: {traceback.format_exc()}")

                        # Try the command-line approach as fallback
                        self.progress_update.emit("⚠️ Trying command-line approach for disconnect...")
                        if self.vpn_manager.ensure_vpn_off():
                            self.progress_update.emit("✅ VPN disconnected successfully via command-line")

                            # Verify disconnection by checking new IP
                            try:
                                time.sleep(2)  # Wait for disconnection to take effect
                                new_ip = self.vpn_manager.get_current_ip()
                                if new_ip and current_ip and new_ip != current_ip:
                                    self.progress_update.emit(f"IP changed after disconnection: {new_ip}")
                            except Exception:
                                pass
                        else:
                            self.progress_update.emit("⚠️ Failed to disconnect from VPN")
                            self.progress_update.emit("Check the logs at hma/vpn_manager.log and hma/hma_commands.log for details")
            except Exception as e:
                import traceback
                self.progress_update.emit(f"⚠️ VPN error during disconnect: {str(e)}")
                self.progress_update.emit(f"Error details: {traceback.format_exc()}")

        # Signal that we're done
        self.finished.emit()

    def save_failed_sender(self, sender, error_message):
        """Save failed sender to a text file with timestamp and error message, avoiding duplicates"""
        import datetime
        import os

        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        new_entry = f"{timestamp} | {sender} | {error_message}\n"

        try:
            # Check if file exists
            if not os.path.exists(self.failed_senders_file):
                # Create new file with headers
                with open(self.failed_senders_file, 'w', encoding='utf-8') as file:
                    file.write("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")
                    file.write(new_entry)
                return

            # Check for duplicates (same sender and error, ignoring timestamp)
            existing_entries = []
            duplicate_found = False

            with open(self.failed_senders_file, 'r', encoding='utf-8') as file:
                existing_entries = file.readlines()

            for entry in existing_entries:
                if entry.startswith('#') or not entry.strip():
                    continue

                # Extract sender and error from existing entry
                parts = entry.split(' | ', 2)
                if len(parts) >= 3 and parts[1] == sender and parts[2].strip() == error_message:
                    duplicate_found = True
                    break

            # Only add if not a duplicate
            if not duplicate_found:
                with open(self.failed_senders_file, 'a', encoding='utf-8') as file:
                    file.write(new_entry)
        except Exception as e:
            self.progress_update.emit(f"Error saving failed sender: {str(e)}")

    def cancel_check(self):
        """Cancel the health check"""
        self.cancel = True


class SlowSendManager:
    """Manages the GMX Slow Send feature with rate limiting and round-robin account switching"""

    def __init__(self, emails_per_batch=20, cooldown_hours=1):
        self.emails_per_batch = emails_per_batch
        self.cooldown_hours = cooldown_hours
        self.cooldown_seconds = cooldown_hours * 3600

        # Account tracking
        self.account_last_used = {}  # {account_email: timestamp}
        self.current_account_index = 0
        self.emails_sent_current_batch = 0

        # Enhanced error tracking and quarantine system
        self.account_errors = {}  # {account_email: [{'timestamp': float, 'error_type': str, 'error_code': str}]}
        self.quarantined_accounts = {}  # {account_email: {'quarantine_start': timestamp, 'quarantine_duration': seconds, 'reason': str}}
        self.error_thresholds = {
            'rate_limit_errors': 3,  # 450 errors within time window
            'auth_errors': 2,        # Authentication failures
            'connection_errors': 5   # Connection/timeout errors
        }
        self.quarantine_durations = {
            'rate_limit': 4 * 3600,    # 4 hours for rate limiting (450 errors)
            'auth_failure': 2 * 3600,  # 2 hours for auth failures
            'connection': 1 * 3600     # 1 hour for connection issues
        }
        self.error_window = 10 * 60  # 10 minutes window for error counting

        # State file for persistence
        self.state_file = f"{home}/slow_send_state.json"
        self.load_state()

    def load_state(self):
        """Load the slow send state from file"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    state = json.load(f)
                    self.account_last_used = state.get('account_last_used', {})
                    self.current_account_index = state.get('current_account_index', 0)
                    self.emails_sent_current_batch = state.get('emails_sent_current_batch', 0)
                    self.account_errors = state.get('account_errors', {})
                    self.quarantined_accounts = state.get('quarantined_accounts', {})
        except Exception as e:
            logging.error(f"Error loading slow send state: {e}")
            self.account_last_used = {}
            self.current_account_index = 0
            self.emails_sent_current_batch = 0
            self.account_errors = {}
            self.quarantined_accounts = {}

    def save_state(self):
        """Save the slow send state to file"""
        try:
            state = {
                'account_last_used': self.account_last_used,
                'current_account_index': self.current_account_index,
                'emails_sent_current_batch': self.emails_sent_current_batch,
                'account_errors': self.account_errors,
                'quarantined_accounts': self.quarantined_accounts
            }
            with open(self.state_file, 'w') as f:
                json.dump(state, f)
        except Exception as e:
            logging.error(f"Error saving slow send state: {e}")

    def get_next_account(self, accounts):
        """Get the next account to use based on round-robin and cooldown logic"""
        if not accounts:
            return None, "No accounts available"

        current_time = time.time()

        # If we haven't sent the full batch yet, continue with current account
        if self.emails_sent_current_batch < self.emails_per_batch:
            if self.current_account_index < len(accounts):
                account = accounts[self.current_account_index]
                account_email = account.split(';')[0] if ';' in account else account.split(':')[0]
                return account, f"Using account {account_email} ({self.emails_sent_current_batch + 1}/{self.emails_per_batch})"

        # Need to switch to next account
        self.current_account_index = (self.current_account_index + 1) % len(accounts)
        self.emails_sent_current_batch = 0

        # Check if we've cycled through all accounts
        if self.current_account_index == 0 and len(self.account_last_used) >= len(accounts):
            # Check if first account is ready (1 hour cooldown)
            first_account = accounts[0]
            first_account_email = first_account.split(';')[0] if ';' in first_account else first_account.split(':')[0]

            if first_account_email in self.account_last_used:
                time_since_last_use = current_time - self.account_last_used[first_account_email]
                if time_since_last_use < self.cooldown_seconds:
                    remaining_time = self.cooldown_seconds - time_since_last_use
                    remaining_minutes = int(remaining_time / 60)
                    remaining_seconds = int(remaining_time % 60)
                    return None, f"Waiting for cooldown: {remaining_minutes}m {remaining_seconds}s remaining"

        # Get the current account
        account = accounts[self.current_account_index]
        account_email = account.split(';')[0] if ';' in account else account.split(':')[0]

        return account, f"Switched to account {account_email} (1/{self.emails_per_batch})"

    def record_email_sent(self, account):
        """Record that an email was sent using the specified account"""
        account_email = account.split(';')[0] if ';' in account else account.split(':')[0]
        self.account_last_used[account_email] = time.time()
        self.emails_sent_current_batch += 1
        self.save_state()

    def get_status_info(self, accounts):
        """Get current status information for display"""
        if not accounts:
            return "No accounts available"

        current_time = time.time()

        if self.current_account_index < len(accounts):
            current_account = accounts[self.current_account_index]
            account_email = current_account.split(';')[0] if ';' in current_account else current_account.split(':')[0]

            # Check if we need to wait for cooldown
            if self.current_account_index == 0 and len(self.account_last_used) >= len(accounts):
                if account_email in self.account_last_used:
                    time_since_last_use = current_time - self.account_last_used[account_email]
                    if time_since_last_use < self.cooldown_seconds:
                        remaining_time = self.cooldown_seconds - time_since_last_use
                        remaining_minutes = int(remaining_time / 60)
                        remaining_seconds = int(remaining_time % 60)
                        return f"Cooldown: {remaining_minutes}m {remaining_seconds}s"

            return f"Account: {account_email} ({self.emails_sent_current_batch}/{self.emails_per_batch})"

        return "Ready"

    def reset(self):
        """Reset the slow send state"""
        self.account_last_used = {}
        self.current_account_index = 0
        self.emails_sent_current_batch = 0
        self.save_state()


class Worker(QObject):
    finished = pyqtSignal()
    log_message = pyqtSignal(str)
    show_message_box_signal = pyqtSignal()
    pass_result_to_worker_signal = pyqtSignal(bool)
    cancel_send_signal = pyqtSignal()
    stop_signal = pyqtSignal()
    all_emails_sent_signal = pyqtSignal()
    slow_send_status_update = pyqtSignal(str)  # New signal for status updates



    def __init__(self,messageBox,subject_value, from_value, limit_spin_value,delay_value, html_file_name, groups_file,test_mode_value,isp_value,senders_file_name,rotation_checked, use_vpn=False, emails_per_ip_change=5, gmx_slow_send=False, emails_per_batch=20):
        super().__init__()
        self.subject_value = subject_value
        self.from_value = from_value
        self.limit_spin_value = limit_spin_value
        self.html_file_name = html_file_name
        self.groups_file_name = groups_file
        self.test_mode_value = test_mode_value
        self.isp_value = isp_value
        self.delay_spin = delay_value
        self.senders_file_name = senders_file_name
        self.rotation_checked = rotation_checked
        self.failed_senders_file = f"{home}/failed_senders.txt"
        self.use_vpn = use_vpn
        self.emails_per_ip_change = emails_per_ip_change
        self.emails_sent_since_ip_change = 0

        # GMX Slow Send feature
        self.gmx_slow_send = gmx_slow_send
        self.emails_per_batch = emails_per_batch
        self.slow_send_manager = None
        if self.gmx_slow_send and self.isp_value == "Gmx":
            self.slow_send_manager = SlowSendManager(emails_per_batch)

        # Initialize VPN manager if needed
        if self.use_vpn:
            try:
                import sys
                import os
                # Add the parent directory to sys.path
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from hma.simple_vpn import vpn_manager
                self.vpn_manager = vpn_manager
                # Configure VPN manager
                self.vpn_manager.change_ip_after = self.emails_per_ip_change
                self.log_message.emit("VPN manager initialized")
            except Exception as e:
                self.log_message.emit(f"⚠️ Failed to initialize VPN manager: {str(e)}")
                self.use_vpn = False
        self.cancel_send = False
        self.messageBox = messageBox
        self.pass_result_to_worker_signal.connect(self.handle_message_box_result)
        self.cancel_send_signal.connect(self.handle_cancel_request)
        self.stop_signal.connect(self.handle_cancel_request)
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger("Worker")



    def handle_message_box_result(self, result):
        if result:
            return
        else:
            self.cancel_send_signal.emit()



    def handle_cancel_request(self):
        self.cancel_send = True

    def save_failed_sender(self, sender, error_message):
        """Save failed sender to a text file with timestamp and error message, avoiding duplicates"""
        import datetime
        import os

        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        new_entry = f"{timestamp} | {sender} | {error_message}\n"

        try:
            # Check if file exists
            if not os.path.exists(self.failed_senders_file):
                # Create new file with headers
                with open(self.failed_senders_file, 'w', encoding='utf-8') as file:
                    file.write("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")
                    file.write(new_entry)
                self.log_message.emit(f"Created new failed senders file: {self.failed_senders_file}")
                return

            # Check for duplicates (same sender and error, ignoring timestamp)
            existing_entries = []
            duplicate_found = False

            with open(self.failed_senders_file, 'r', encoding='utf-8') as file:
                existing_entries = file.readlines()

            for entry in existing_entries:
                if entry.startswith('#') or not entry.strip():
                    continue

                # Extract sender and error from existing entry
                parts = entry.split(' | ', 2)
                if len(parts) >= 3 and parts[1] == sender and parts[2].strip() == error_message:
                    duplicate_found = True
                    break

            # Only add if not a duplicate
            if not duplicate_found:
                with open(self.failed_senders_file, 'a', encoding='utf-8') as file:
                    file.write(new_entry)
                self.log_message.emit(f"Saved failed sender to {self.failed_senders_file}")

        except Exception as e:
            self.log_message.emit(f"Error saving failed sender: {str(e)}")



    def run(self):
        # Import required modules
        import sys
        import os
        import traceback

        gmail_isps = []
        yahoo_isps = []
        other_isps = []
        gmx_isps = []

        # Initialize VPN if needed and if using GMX
        if self.use_vpn and self.isp_value == "Gmx":
            try:
                self.log_message.emit("Initializing VPN connection for GMX senders...")

                # Try to connect to VPN using GUI automation
                try:
                    # Add the parent directory to sys.path if not already done
                    if os.path.dirname(os.path.dirname(os.path.abspath(__file__))) not in sys.path:
                        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    from hma.turn_on import turn_on_vpn

                    # Call the GUI automation function to turn on VPN
                    if turn_on_vpn():
                        # Get and display the current IP
                        try:
                            current_ip = self.vpn_manager.get_current_ip()
                            if current_ip:
                                self.log_message.emit(f"✅ VPN connected successfully via GUI automation (IP: {current_ip})")
                            else:
                                self.log_message.emit("✅ VPN connected successfully via GUI automation (IP: unknown)")
                        except Exception as e:
                            self.log_message.emit("✅ VPN connected successfully via GUI automation (could not determine IP)")
                    else:
                        # If GUI automation fails, try the command-line approach as fallback
                        self.log_message.emit("⚠️ GUI automation failed, trying command-line approach...")
                        if self.vpn_manager.ensure_vpn_on():
                            # Get and display the current IP
                            try:
                                current_ip = self.vpn_manager.get_current_ip()
                                if current_ip:
                                    self.log_message.emit(f"✅ VPN connected successfully via command-line (IP: {current_ip})")
                                else:
                                    self.log_message.emit("✅ VPN connected successfully via command-line (IP: unknown)")
                            except Exception as e:
                                self.log_message.emit("✅ VPN connected successfully via command-line (could not determine IP)")
                        else:
                            self.log_message.emit("❌ Failed to connect to VPN")
                            self.use_vpn = False
                except Exception as e:
                    import traceback
                    self.log_message.emit(f"❌ Error during GUI automation: {str(e)}")
                    self.log_message.emit(f"Error details: {traceback.format_exc()}")

                    # Try the command-line approach as fallback
                    self.log_message.emit("⚠️ Trying command-line approach instead...")
                    if self.vpn_manager.ensure_vpn_on():
                        # Get and display the current IP
                        try:
                            current_ip = self.vpn_manager.get_current_ip()
                            if current_ip:
                                self.log_message.emit(f"✅ VPN connected successfully via command-line (IP: {current_ip})")
                            else:
                                self.log_message.emit("✅ VPN connected successfully via command-line (IP: unknown)")
                        except Exception as e:
                            self.log_message.emit("✅ VPN connected successfully via command-line (could not determine IP)")
                    else:
                        self.log_message.emit("❌ Failed to connect to VPN")
                        self.use_vpn = False
            except Exception as e:
                self.log_message.emit(f"❌ VPN error: {str(e)}")
                self.use_vpn = False

        data_file = f"{home}/email-test.txt" if self.test_mode_value else f"{home}/{self.groups_file_name}"
        with open(data_file, 'r') as data_file:
            email_addresses = data_file.readlines()
        with open(f"{home}/{self.senders_file_name}") as senders_file:
            senders = senders_file.readlines()
        for sender in senders:
            if "@gmail.com" in sender:
                gmail_isps.append(sender.strip())
            elif "@yahoo.com" in sender:
                yahoo_isps.append(sender.strip())
            elif "@gmx.com" in sender:
                gmx_isps.append(sender.strip())
            else:
                other_isps.append(sender.strip())
        if self.isp_value == "Gmail":
            senders_acc = gmail_isps
            smtp_host = "smtp.gmail.com"
        if self.isp_value == "Gmx":
            senders_acc = gmx_isps
            smtp_host = "mail.gmx.com"
        elif self.isp_value == "Yahoo":
            senders_acc = yahoo_isps
            smtp_host = "smtp.mail.yahoo.com"
        else:
            senders_acc = other_isps
            smtp_host = "smtp.office365.com"
        if self.test_mode_value is False:
            needed_senders_number = int(len(email_addresses) / self.limit_spin_value)
            if needed_senders_number > len(senders_acc):
                self.show_message_box_signal.emit()
        sender_index = 0
        sender_count = 0
        all_emails_sent = 0
        #to_repeat = int(len(email_addresses)/150)
        # Always use the limit_spin_value for rotation when rotation is enabled
        # The rotation checkbox determines whether to rotate at all
        emails_per_sender = self.limit_spin_value

        # Initialize slow send status
        if self.slow_send_manager:
            self.slow_send_status_update.emit(self.slow_send_manager.get_status_info(senders_acc))

        for to_addr in email_addresses:
            if self.cancel_send:
                break
            to_addr = to_addr.strip()

            # Handle slow send mode for GMX
            if self.slow_send_manager:
                sender_info, status_message = self.slow_send_manager.get_next_account(senders_acc)
                if sender_info is None:
                    # Need to wait for cooldown
                    self.log_message.emit(f"GMX Slow Send: {status_message}")
                    self.slow_send_status_update.emit(status_message)

                    # Wait for a short period and check again
                    sleep(60)  # Wait 1 minute before checking again
                    continue
                else:
                    self.log_message.emit(f"GMX Slow Send: {status_message}")
                    self.slow_send_status_update.emit(status_message)
            else:
                # Standard rotation logic
                if self.rotation_checked and sender_count >= emails_per_sender:
                    sender_index = (sender_index + 1) % len(senders_acc)
                    sender_count = 0
                    # Log the rotation but don't try to parse the sender yet
                    self.log_message.emit(f"Rotating to next sender (index {sender_index})")
                sender_info = senders_acc[sender_index]

            try:
                # Try to split by semicolon first, then by colon if that fails
                if ';' in sender_info:
                    sender, passwd = sender_info.split(';')
                elif ':' in sender_info:
                    sender, passwd = sender_info.split(':')
                else:
                    raise ValueError("Sender info doesn't contain separator")
                there_is_senders = True
                # Now that we've successfully parsed the sender, log it
                self.log_message.emit(f"Using sender: {sender}")
            except Exception as e:
                there_is_senders = False
                self.log_message.emit(f"No {self.isp_value} Senders!! Error: {str(e)}")
            if there_is_senders is True:
                #email_list = ','.join([email.replace('\n', '') for email in email_addresses[:100]])
                with open(f"{home}/{self.html_file_name}", 'r', encoding='utf-8') as html_content_file:
                    html_content = html_content_file.read()
                msg = MIMEMultipart("alternative")
                html_body = MIMEText(html_content, "html")
                msg.attach(html_body)
                msg["Subject"] = self.subject_value
                msg['From'] = formataddr((self.from_value, f'{sender}'))
                msg["To"] = to_addr
                context = ssl.create_default_context()
                with smtplib.SMTP(smtp_host, port=587) as smtp:
                    smtp.starttls(context=context)
                    try:
                        # Use new OAuth2-aware authentication
                        authenticate_smtp(smtp, sender, passwd, self.log_message.emit)
                        self.smtp_logged_in = True
                        self.logger.info(f"Smtp Login Success >> {sender}")
                    except Exception as e:
                        error_msg = str(e)
                        self.log_message.emit(f"Smtp Login Failed >> {sender}")
                        self.logger.error(f"Error Login using {sender} >> {error_msg}")
                        # Save the failed sender to the file
                        self.save_failed_sender(sender, error_msg)
                        self.smtp_logged_in = False
                    if self.smtp_logged_in is True and not self.cancel_send:
                        try:
                            # Check if we need to change IP for GMX senders
                            if self.use_vpn and self.isp_value == "Gmx":
                                self.emails_sent_since_ip_change += 1
                                if self.emails_sent_since_ip_change >= self.emails_per_ip_change:
                                    self.log_message.emit(f"\n🔄 Changing IP address after {self.emails_sent_since_ip_change} emails...")
                                    try:
                                        # Try to change IP using GUI automation
                                        try:
                                            # Add the parent directory to sys.path if not already done
                                            if os.path.dirname(os.path.dirname(os.path.abspath(__file__))) not in sys.path:
                                                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                                            from hma.turn_on import turn_on_vpn
                                            from hma.turn_off import turn_off_vpn

                                            # First disconnect and then connect again to change IP
                                            self.log_message.emit("Disconnecting VPN to change IP...")
                                            if turn_off_vpn():
                                                self.log_message.emit("VPN disconnected, now reconnecting...")
                                                time.sleep(2)  # Wait a bit before reconnecting

                                                if turn_on_vpn():
                                                    self.log_message.emit("✅ IP address changed successfully via GUI automation")
                                                else:
                                                    self.log_message.emit("❌ Failed to reconnect VPN after disconnecting")
                                                    # Try the command-line approach as fallback
                                                    self.log_message.emit("⚠️ Trying command-line approach instead...")
                                                    if self.vpn_manager.change_ip_if_needed(force=True):
                                                        self.log_message.emit("✅ IP address changed successfully via command-line")
                                                    else:
                                                        self.log_message.emit("❌ Failed to change IP address")
                                            else:
                                                self.log_message.emit("❌ Failed to disconnect VPN for IP change")
                                                # Try the command-line approach as fallback
                                                self.log_message.emit("⚠️ Trying command-line approach instead...")
                                                if self.vpn_manager.change_ip_if_needed(force=True):
                                                    self.log_message.emit("✅ IP address changed successfully via command-line")
                                                else:
                                                    self.log_message.emit("❌ Failed to change IP address")
                                        except Exception as e:
                                            import traceback
                                            self.log_message.emit(f"❌ Error during GUI automation for IP change: {str(e)}")
                                            self.log_message.emit(f"Error details: {traceback.format_exc()}")

                                            # Try the command-line approach as fallback
                                            self.log_message.emit("⚠️ Trying command-line approach instead...")
                                            if self.vpn_manager.change_ip_if_needed(force=True):
                                                self.log_message.emit("✅ IP address changed successfully via command-line")
                                            else:
                                                self.log_message.emit("❌ Failed to change IP address")
                                    except Exception as e:
                                        self.log_message.emit(f"❌ Error changing IP: {str(e)}")
                                    self.emails_sent_since_ip_change = 0
                                    # Close the current SMTP connection
                                    smtp.close()
                                    # Reconnect with the new IP
                                    smtp = smtplib.SMTP(smtp_host, port=587)
                                    smtp.starttls(context=context)
                                    # Use new OAuth2-aware authentication
                                    authenticate_smtp(smtp, sender, passwd, self.log_message.emit)

                            # Send the email
                            smtp.sendmail(sender, to_addr, msg.as_string())
                            self.log_message.emit(f"Email Sent using >> {sender} to {to_addr}")
                            self.logger.info(f"Email Sent using >> {sender} to {to_addr}")
                            all_emails_sent += 1
                            self.log_message.emit(f"Emails Sent >> {all_emails_sent} / {len(email_addresses)}")

                            # Record email sent for slow send mode
                            if self.slow_send_manager:
                                self.slow_send_manager.record_email_sent(sender_info)
                                self.slow_send_status_update.emit(self.slow_send_manager.get_status_info(senders_acc))

                            smtp.close()
                        except Exception as e:
                            error_msg = str(e)
                            self.log_message.emit(f"Email Not Sent >> {sender}!!")
                            self.logger.error(f"Error Email Not Sent >> {sender} {error_msg}")

                            # Check if this is a Microsoft spam filtering issue
                            if "OutboundSpamException" in error_msg or "WASCL" in error_msg:
                                self.log_message.emit(f"⚠️ Microsoft spam filter detected for {sender}")
                                self.log_message.emit(f"💡 This is not an authentication issue - account may have sending restrictions")
                                self.log_message.emit(f"💡 Consider waiting 24 hours or reducing sending frequency")

                                # Create a temporary OAuth2 handler to analyze the error
                                try:
                                    from Files.smtp import OAuth2Handler
                                    temp_handler = OAuth2Handler()
                                    temp_handler._analyze_spam_error(error_msg)
                                except:
                                    pass  # If analysis fails, continue with normal error handling

                            # Save the failed sender to the file
                            self.save_failed_sender(sender, f"Send failed: {error_msg}")
                    sender_count += 1
                    if self.rotation_checked and sender_count == emails_per_sender:
                        smtp.close()
                if len(email_addresses) > 1 and self.smtp_logged_in is True :
                    sleep(self.delay_spin)
                #break
        self.all_emails_sent_signal.emit()

        # Turn off VPN if it was used
        if self.use_vpn and self.isp_value == "Gmx":
            try:
                self.log_message.emit("\nDisconnecting from VPN...")

                # Try to disconnect using GUI automation
                try:
                    # Add the parent directory to sys.path if not already done
                    if os.path.dirname(os.path.dirname(os.path.abspath(__file__))) not in sys.path:
                        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    from hma.turn_off import turn_off_vpn

                    # Call the GUI automation function to turn off VPN
                    if turn_off_vpn():
                        self.log_message.emit("✅ VPN disconnected successfully via GUI automation")
                    else:
                        # If GUI automation fails, try the command-line approach as fallback
                        self.log_message.emit("⚠️ GUI automation failed for disconnect, trying command-line approach...")
                        if self.vpn_manager.ensure_vpn_off():
                            self.log_message.emit("✅ VPN disconnected successfully via command-line")
                        else:
                            self.log_message.emit("⚠️ Failed to disconnect from VPN")
                except Exception as e:
                    import traceback
                    self.log_message.emit(f"❌ Error during GUI automation for disconnect: {str(e)}")
                    self.log_message.emit(f"Error details: {traceback.format_exc()}")

                    # Try the command-line approach as fallback
                    self.log_message.emit("⚠️ Trying command-line approach for disconnect...")
                    if self.vpn_manager.ensure_vpn_off():
                        self.log_message.emit("✅ VPN disconnected successfully via command-line")
                    else:
                        self.log_message.emit("⚠️ Failed to disconnect from VPN")
            except Exception as e:
                self.log_message.emit(f"⚠️ VPN error during disconnect: {str(e)}")

        self.finished.emit()



class MainWindow(QMainWindow):
    return_result_signal = pyqtSignal(int)

    def __init__(self):
        super().__init__()
        uic.loadUi(f"{home}/Smtp.ui", self)
        self.html_file_name = "offre.html"
        self.html_file_path = f"{home}/{self.html_file_name}"
        self.senders_file_name = "senders.txt"
        self.groups_file_name = "groups.txt"
        self.groups_test_file_name = "email-test.txt"
        self.saved_data_js = f"{home}/saved_data.json"
        self.failed_senders_file = f"{home}/failed_senders.txt"
        self.bounced_emails_file = f"{home}/bounced_emails.txt"
        self.messageBox = QMessageBox()
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger("App")

        # Check for required dependencies
        self.check_dependencies()

        self.setup_ui()
        self.load_saved_data()
        self.return_result_signal.connect(self.handle_returned_result)

    def check_dependencies(self):
        """Check if all required dependencies are installed"""

        # Check if pywinauto is installed
        try:
            import pywinauto
            self.logger.info("pywinauto is installed")
        except ImportError:
            # Show a message box about missing dependencies
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                "Missing Dependencies",
                "The pywinauto package is required for automatic VPN control.\n\n"
                "Please install it by running:\n"
                "pip install pywinauto\n\n"
                "The application will still work, but VPN control may be limited."
            )

    def setup_copyable_text_area(self, text_widget):
        """Setup a text widget to be copyable with keyboard shortcuts"""
        # For read-only widgets, enable text selection
        if text_widget.isReadOnly():
            text_widget.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse | Qt.TextInteractionFlag.TextSelectableByKeyboard)
        else:
            # For editable widgets, enable all text interactions (includes selection and editing)
            text_widget.setTextInteractionFlags(Qt.TextInteractionFlag.TextEditorInteraction)

        text_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.DefaultContextMenu)

        # Add Ctrl+A shortcut for select all
        select_all_shortcut = QShortcut(QKeySequence.StandardKey.SelectAll, text_widget)
        select_all_shortcut.activated.connect(text_widget.selectAll)

        # Add Ctrl+C shortcut for copy
        copy_shortcut = QShortcut(QKeySequence.StandardKey.Copy, text_widget)
        copy_shortcut.activated.connect(text_widget.copy)

    def setup_ui(self):
        old_style = self.rotation_check.styleSheet()
        new_css = f"""
        QCheckBox::indicator:checked {{
            background-image: url("{home}/img/check.svg");
        }}
        """
        arrows_img = f"""
            QSpinBox::up-arrow, QSpinBox::down-arrow {{
                image: url("{home}/img/up.svg");
            }}
            QSpinBox::down-arrow {{
                image: url("{home}/img/down.svg");
            }}
            QSpinBox::up-arrow:hover {{
                image: url("{home}/img/up.svg");
            }}
            QSpinBox::down-arrow:hover {{
                image: url("{home}/img/down.svg");
            }}
        """
        self.limit_spin.setStyleSheet(self.limit_spin.styleSheet()+arrows_img)
        self.delay_spin.setStyleSheet(self.delay_spin.styleSheet()+arrows_img)
        new_check_img = old_style + new_css
        self.test_mode.setStyleSheet(new_check_img)
        self.only_groups.setStyleSheet(new_check_img)
        self.rotation_check.setStyleSheet(new_check_img)
        self.gmx_slow_send.setStyleSheet(new_check_img)
        self.emails_per_batch_spin.setStyleSheet(self.emails_per_batch_spin.styleSheet()+arrows_img)
        edit_icon = QIcon(f"{home}/img/edit.svg")
        self.edit_groups.setIcon(edit_icon)
        self.edit_senders.setIcon(edit_icon)
        self.edit_test_emails.setIcon(edit_icon)
        self.edit_btn.clicked.connect(self.editBtn)
        self.preview_btn.clicked.connect(self.prevBtn)
        self.edit_groups.clicked.connect(partial(self.editfile, "Groups"))
        self.edit_senders.clicked.connect(partial(self.editfile, "Senders"))
        self.edit_test_emails.clicked.connect(partial(self.editfile, "Test"))

        # Add buttons for failed senders and health check
        from PyQt6.QtWidgets import QPushButton

        button_style = """
            QPushButton {
                padding: 5px 10px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: #f5f5f5;
                color: #000;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
                border: 1px solid #aaa;
            }
        """

        self.view_failed_btn = QPushButton("Failed Senders", self)
        self.view_failed_btn.setStyleSheet(button_style)
        self.view_failed_btn.clicked.connect(self.view_failed_senders)

        self.health_check_btn = QPushButton("Health Check", self)
        self.health_check_btn.setStyleSheet(button_style)
        self.health_check_btn.clicked.connect(self.health_check_senders)

        self.bounce_collector_btn = QPushButton("Bounce", self)
        self.bounce_collector_btn.setStyleSheet(button_style)
        self.bounce_collector_btn.clicked.connect(self.bounce_collector)

        # Add the buttons to the layout
        self.horizontalLayoutWidget_7.layout().addWidget(self.view_failed_btn)
        self.horizontalLayoutWidget_7.layout().addWidget(self.health_check_btn)
        self.horizontalLayoutWidget_7.layout().addWidget(self.bounce_collector_btn)
        with open(f"{home}/{self.senders_file_name}", 'r') as send_file:
            self.senders_number = len(send_file.readlines())
        send_file.close()
        self.senders_num.setText(str(self.senders_number))
        with open(f"{home}/{self.groups_file_name}", 'r') as groups_file:
            self.groups_number = len(groups_file.readlines())
        groups_file.close()
        self.groups_num.setText(str(self.groups_number))
        with open(f"{home}/{self.groups_test_file_name}", 'r') as test_groups_file:
            self.test_groups_number = len(test_groups_file.readlines())
        test_groups_file.close()
        try:
            self.send_btn.clicked.connect(self.send)
        except Exception as e:
            self.logger.error(f"Error while trying to send >> {str(e)}")
            QMessageBox.critical(self,"Error","Please Check Logs")
        self.test_mode.stateChanged.connect(self.test_mode_check)
        self.log_text.setReadOnly(True)
        self.setup_copyable_text_area(self.log_text)
        self.log_text.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        self.log_text.setPlainText(f"Starting Smtp..." + "\n")


    def getting_started(self):
        if self.senders_number == 0:
            QMessageBox.critical(self,"Error Senders File Empty","Please Add at least One Sender!!")
        if self.groups_number == 0:
            self.log_text.setPlainText(f"Groups File is Empty!!" + "\n")
        if self.test_groups_number == 0:
            self.log_text.setPlainText(f"Test Groups File is Emtpy!!" + "\n")


    def showEvent(self, event):
        super().showEvent(event)
        QTimer.singleShot(0, self.getting_started)



    def show_message_box(self):
        msg_box = QMessageBox()
        msg_box.setWindowTitle("Senders Number is Low")
        msg_box.setText("Senders Number is Low you need more, Do you want to proceed with these numbers?")
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)
        result = msg_box.exec()
        self.return_result_signal.emit(result)



    def handle_returned_result(self, result):
        if result == QMessageBox.StandardButton.Ok:
            self.worker.pass_result_to_worker_signal.emit(True)
        else:
            self.worker.pass_result_to_worker_signal.emit(False)



    def send(self):
        if self.subject_input.text() == "" or self.from_input.text() == "":
            QMessageBox.critical(self,"Error", f"Please Check Your Subject & From")
        else:
            # Ask about using VPN for GMX senders
            use_vpn = False
            emails_per_ip_change = 5

            if self.isp_list.currentText() == "Gmx":
                from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel, QSpinBox, QPushButton

                vpn_dialog = QDialog(self)
                vpn_dialog.setWindowTitle("VPN Options")
                vpn_dialog.setFixedSize(400, 200)
                layout = QVBoxLayout(vpn_dialog)

                # VPN checkbox
                use_vpn_checkbox = QCheckBox("Use VPN (HMA) for GMX senders", vpn_dialog)
                use_vpn_checkbox.setChecked(True)
                layout.addWidget(use_vpn_checkbox)

                # Emails per IP change
                ip_change_layout = QHBoxLayout()
                emails_per_ip_label = QLabel("Emails per IP change:", vpn_dialog)
                ip_change_layout.addWidget(emails_per_ip_label)

                emails_per_ip_spin = QSpinBox(vpn_dialog)
                emails_per_ip_spin.setMinimum(1)
                emails_per_ip_spin.setMaximum(20)
                emails_per_ip_spin.setValue(5)
                ip_change_layout.addWidget(emails_per_ip_spin)
                layout.addLayout(ip_change_layout)

                # Buttons
                button_layout = QHBoxLayout()
                ok_button = QPushButton("OK", vpn_dialog)
                cancel_button = QPushButton("Cancel", vpn_dialog)
                button_layout.addWidget(ok_button)
                button_layout.addWidget(cancel_button)
                layout.addLayout(button_layout)

                # Connect buttons
                ok_button.clicked.connect(vpn_dialog.accept)
                cancel_button.clicked.connect(vpn_dialog.reject)

                # Show dialog
                if vpn_dialog.exec() == QDialog.DialogCode.Accepted:
                    use_vpn = use_vpn_checkbox.isChecked()
                    emails_per_ip_change = emails_per_ip_spin.value()

            self.save_data_to_json()
            self.thread = QThread()
            self.worker = Worker(
                self.messageBox,
                self.subject_input.text(),
                self.from_input.text(),
                self.limit_spin.value(),
                self.delay_spin.value(),
                self.html_file_name,
                self.groups_file_name,
                self.test_mode.isChecked(),
                self.isp_list.currentText(),
                self.senders_file_name,
                self.rotation_check.isChecked(),
                use_vpn,
                emails_per_ip_change,
                self.gmx_slow_send.isChecked(),
                self.emails_per_batch_spin.value()
            )
            self.worker.log_message.connect(self.update_log_text)
            self.worker.show_message_box_signal.connect(self.show_message_box)
            self.stop_btn.clicked.connect(self.worker.stop_signal.emit)
            self.worker.all_emails_sent_signal.connect(self.show_all_emails_sent_message)
            self.worker.slow_send_status_update.connect(self.update_slow_send_status)
            self.worker.moveToThread(self.thread)
            self.thread.started.connect(self.worker.run)
            self.worker.finished.connect(self.thread.quit)
            self.worker.finished.connect(self.worker.deleteLater)
            self.thread.finished.connect(self.thread.deleteLater)
            try:
                self.thread.start()
            except Exception as e:
                self.logger.error(f"Worker Error >> {str(e)}")
            self.send_btn.setEnabled(False)
            self.thread.finished.connect(
                lambda: self.send_btn.setEnabled(True)
            )



    def update_log_text(self,message):
        self.log_text.setPlainText(self.log_text.toPlainText() + message + "\n")

    def update_slow_send_status(self, status):
        """Update the slow send status label"""
        self.slow_send_status.setText(f"Status: {status}")




    def show_all_emails_sent_message(self):
        QMessageBox.information(self, "Sender", "All Emails Sent successfully.")




    def editfile(self,file):
        dialog = QDialog(self)
        dialog.setFixedSize(700, 500)
        dialog.setWindowTitle(f"Edit {file}")
        layout = QVBoxLayout(dialog)
        text_editor = QPlainTextEdit(self)
        # Enable copying for editable text areas too
        self.setup_copyable_text_area(text_editor)
        layout.addWidget(text_editor)
        if file == "Senders":
            file_name = self.senders_file_name
        elif file == "Groups":
            file_name = self.groups_file_name
        else:
            file_name = self.groups_test_file_name
        file_path = f"{home}/{file_name}"
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text_content = file.read()
                text_editor.setPlainText(text_content)
        except Exception as e:
            self.logger.error(f"Error while opening {file} >> {str(e)}")
        save_button = QPushButton("Save", dialog)
        layout.addWidget(save_button)
        save_button.clicked.connect(lambda: self.saveEditedFile(text_editor,file_path))
        save_button.clicked.connect(lambda: self.close_window(dialog))
        dialog.exec()


    def test_mode_check(self, state):
        if state == 2:
            self.log_text.setPlainText(self.log_text.toPlainText() + "Entering Test Mode!!" + "\n")
            with open(f"{home}/{self.groups_test_file_name}", 'r') as test_groups_file:
                self.test_groups_number = len(test_groups_file.readlines())
                if self.test_groups_number == 0:
                    QMessageBox.critical(self,"Error", f"Please add at least one Test email!!")
        else:
            self.log_text.setPlainText(self.log_text.toPlainText() + "Entering Normal Mode!!" + "\n")


    def prevBtn(self):
        """Preview the HTML file in a web view or text editor"""
        if WEBENGINE_AVAILABLE:
            # Use WebEngine for rich HTML preview
            dialog = QDialog(self)
            dialog.setFixedSize(800, 700)
            dialog.setWindowTitle("HTML Preview")
            layout = QVBoxLayout(dialog)
            web_view = QWebEngineView(dialog)
            layout.addWidget(web_view)
            settings = web_view.settings()
            settings = web_view.page().settings()
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
            web_view.setContextMenuPolicy(Qt.ContextMenuPolicy.NoContextMenu)
            file_url = QUrl.fromLocalFile(self.html_file_path)
            web_view.load(file_url)
            dialog.exec()
        else:
            # Fallback to text preview when WebEngine is not available
            try:
                with open(self.html_file_path, 'r', encoding='utf-8') as f:
                    html_content = f.read()

                dialog = QDialog(self)
                dialog.setFixedSize(800, 700)
                dialog.setWindowTitle("HTML Preview (Text Mode)")
                layout = QVBoxLayout(dialog)

                # Add info label
                from PyQt6.QtWidgets import QLabel
                info_label = QLabel("WebEngine not available. Showing HTML source code:", dialog)
                layout.addWidget(info_label)

                # Text area for HTML content
                text_area = QPlainTextEdit(dialog)
                text_area.setPlainText(html_content)
                text_area.setReadOnly(True)
                self.setup_copyable_text_area(text_area)
                layout.addWidget(text_area)

                # Close button
                close_button = QPushButton("Close", dialog)
                close_button.clicked.connect(dialog.accept)
                layout.addWidget(close_button)

                dialog.exec()
            except Exception as e:
                self.messageBox.critical(self, "Error", f"Failed to preview HTML: {str(e)}")
                print(f"HTML preview error: {e}")



    def editBtn(self):
        dialog = QDialog(self)
        dialog.setFixedSize(800, 700)
        dialog.setWindowTitle("Edit Html")
        layout = QVBoxLayout(dialog)
        html_editor = QPlainTextEdit(self)
        # Enable copying for HTML editor
        self.setup_copyable_text_area(html_editor)
        layout.addWidget(html_editor)

        file_path = f"{home}/{self.html_file_name}"
        with open(file_path, 'r', encoding='utf-8') as file:
            html_content = file.read()
            html_editor.setPlainText(html_content)

        save_button = QPushButton("Save", dialog)
        layout.addWidget(save_button)
        save_button.clicked.connect(lambda: self.saveHtmlToFile(html_editor))
        save_button.clicked.connect(lambda: self.close_window(dialog))
        dialog.exec()




    def saveHtmlToFile(self, html_editor):
        edited_html = html_editor.toPlainText()
        try:
            with open(self.html_file_path, 'w', encoding='utf-8') as file:
                file.write(edited_html)
            QMessageBox.information(html_editor, "Save HTML", "HTML content saved to 'offre.html' successfully.")
        except Exception as e:
            QMessageBox.critical(html_editor, "Error", f"Failed to save HTML: {str(e)}")




    def saveEditedFile(self, text_editor,file_path):
        edited_text = text_editor.toPlainText()
        if self.only_groups.isChecked():
            lines = edited_text.split("\n")
            cleaned_lines = [line for line in lines if "@googlegroups.com" in line]
            cleaned_lines = list(dict.fromkeys(cleaned_lines))
            edited_text = "\n".join(cleaned_lines)
        else:
            lines = edited_text.split("\n")
            cleaned_lines = list(dict.fromkeys(lines))
            edited_text = "\n".join(cleaned_lines)

        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(edited_text)
            QMessageBox.information(text_editor, "Save File", "File content saved successfully.")
            if "senders" in file_path:
                self.senders_num.setText(str(len(edited_text.split("\n"))))
            else:
                self.groups_num.setText(str(len(edited_text.split("\n"))))
        except Exception as e:
            QMessageBox.critical(text_editor, "Error", f"Failed to save File: {str(e)}")



    def close_window(self, dialog):
        dialog.accept()

    def view_failed_senders(self):
        """Open the failed senders file in a dialog for viewing and editing"""
        from PyQt6.QtWidgets import QHBoxLayout
        dialog = QDialog(self)
        dialog.setFixedSize(800, 600)
        dialog.setWindowTitle("Failed Senders")
        layout = QVBoxLayout(dialog)
        text_editor = QPlainTextEdit(self)
        # Make the text editor read-only since we're not saving changes from it
        text_editor.setReadOnly(True)
        self.setup_copyable_text_area(text_editor)
        layout.addWidget(text_editor)

        # Check if the file exists, create it if it doesn't
        import os
        if not os.path.exists(self.failed_senders_file):
            with open(self.failed_senders_file, 'w', encoding='utf-8') as file:
                file.write("# Failed senders log\n")
                file.write("# Format: Timestamp | Sender | Error Message\n\n")

        # Load the file content
        try:
            with open(self.failed_senders_file, 'r', encoding='utf-8') as file:
                text_content = file.read()
                text_editor.setPlainText(text_content)
        except Exception as e:
            self.logger.error(f"Error while opening failed senders file >> {str(e)}")
            text_editor.setPlainText(f"Error opening file: {str(e)}")

        # Add buttons
        button_layout = QHBoxLayout()

        export_button = QPushButton("Export Email:Pass", dialog)
        export_button.clicked.connect(lambda: self.export_failed_senders(text_editor.toPlainText()))

        clear_button = QPushButton("Clear", dialog)
        clear_button.clicked.connect(lambda: self.clear_failed_senders_file(text_editor))

        close_button = QPushButton("Close", dialog)
        close_button.clicked.connect(lambda: self.close_window(dialog))

        button_layout.addWidget(export_button)
        button_layout.addWidget(clear_button)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        dialog.exec()

    def export_failed_senders(self, content):
        """Export failed senders in email:pass format"""
        # Create a progress dialog
        from PyQt6.QtWidgets import QProgressDialog
        progress = QProgressDialog("Exporting failed senders...", "Cancel", 0, 100, self)
        progress.setWindowTitle("Export Progress")
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.setMinimumDuration(0)
        progress.setValue(0)
        progress.show()

        # Create a worker class for the export
        class ExportWorker(QObject):
            finished = pyqtSignal(bool, str, int)  # success, message, count
            progress_update = pyqtSignal(int)

            def __init__(self, content, senders_file_name):
                super().__init__()
                self.content = content
                self.senders_file_name = senders_file_name

            def run(self):
                try:
                    # Extract unique email addresses from the failed senders log
                    unique_emails = set()
                    self.progress_update.emit(10)

                    for line in self.content.split('\n'):
                        if line.startswith('#') or not line.strip():
                            continue

                        parts = line.split(' | ', 2)
                        if len(parts) >= 2:
                            email = parts[1].strip()
                            if '@' in email:
                                unique_emails.add(email)

                    if not unique_emails:
                        self.finished.emit(False, "No email addresses found to export.", 0)
                        return

                    self.progress_update.emit(30)

                    # Get the original sender credentials
                    sender_credentials = {}
                    try:
                        with open(self.senders_file_name, 'r') as senders_file:
                            for line in senders_file:
                                line = line.strip()
                                if not line:
                                    continue

                                # Try to parse with different separators
                                if ';' in line:
                                    email, password = line.split(';', 1)
                                    sender_credentials[email.strip()] = password.strip()
                                elif ':' in line:
                                    email, password = line.split(':', 1)
                                    sender_credentials[email.strip()] = password.strip()
                    except Exception as e:
                        self.finished.emit(False, f"Error reading senders file: {str(e)}", 0)
                        return

                    self.progress_update.emit(60)

                    # Create the export file
                    export_file_path = f"{home}/failed_senders_export.txt"
                    with open(export_file_path, 'w', encoding='utf-8') as export_file:
                        for email in sorted(unique_emails):
                            if email in sender_credentials:
                                export_file.write(f"{email}:{sender_credentials[email]}\n")
                            else:
                                export_file.write(f"{email}:password_not_found\n")

                    self.progress_update.emit(100)

                    success_message = f"Failed senders exported to:\n{export_file_path}\n\nTotal emails exported: {len(unique_emails)}"
                    self.finished.emit(True, success_message, len(unique_emails))

                except Exception as e:
                    self.finished.emit(False, f"Failed to export senders: {str(e)}", 0)

        # Create thread and worker
        self.export_thread = QThread()
        self.export_worker = ExportWorker(content, f"{home}/{self.senders_file_name}")

        # Connect signals
        self.export_worker.progress_update.connect(progress.setValue)
        self.export_worker.finished.connect(
            lambda success, message, count: self.handle_export_finished(success, message, count, progress)
        )
        progress.canceled.connect(self.export_thread.quit)

        # Set up the thread
        self.export_worker.moveToThread(self.export_thread)
        self.export_thread.started.connect(self.export_worker.run)
        self.export_worker.finished.connect(self.export_thread.quit)
        self.export_worker.finished.connect(self.export_worker.deleteLater)
        self.export_thread.finished.connect(self.export_thread.deleteLater)

        # Start the thread
        self.export_thread.start()

    def handle_export_finished(self, success, message, _, progress_dialog):
        """Handle the completion of the export process"""
        progress_dialog.close()

        # The third parameter (count) is not used but kept for compatibility
        if success:
            QMessageBox.information(self, "Export Successful", message)
        else:
            QMessageBox.critical(self, "Export Error", message)

    def clear_failed_senders_file(self, text_editor):
        """Clear the failed senders file and reset it with headers"""
        try:
            # Set the text in the editor
            text_editor.setPlainText("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")

            # Also save to the file
            with open(self.failed_senders_file, 'w', encoding='utf-8') as file:
                file.write("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")

            QMessageBox.information(text_editor, "Clear File", "Failed senders file cleared successfully.")
        except Exception as e:
            QMessageBox.critical(text_editor, "Error", f"Failed to clear file: {str(e)}")

    def health_check_senders(self):
        """Test SMTP login for all senders without sending emails"""
        # Create a dialog to show progress and results
        dialog = QDialog(self)
        dialog.setFixedSize(800, 600)
        dialog.setWindowTitle("Sender Health Check")
        layout = QVBoxLayout(dialog)

        # Add options at the top
        from PyQt6.QtWidgets import QHBoxLayout, QCheckBox, QLabel, QSpinBox
        options_layout = QHBoxLayout()

        # VPN checkbox
        use_vpn_checkbox = QCheckBox("Use VPN (HMA)", dialog)
        use_vpn_checkbox.setChecked(True)
        options_layout.addWidget(use_vpn_checkbox)

        # Emails per IP change
        emails_per_ip_label = QLabel("Emails per IP change:", dialog)
        options_layout.addWidget(emails_per_ip_label)

        emails_per_ip_spin = QSpinBox(dialog)
        emails_per_ip_spin.setMinimum(1)
        emails_per_ip_spin.setMaximum(20)
        emails_per_ip_spin.setValue(5)
        options_layout.addWidget(emails_per_ip_spin)

        # Add options to layout
        layout.addLayout(options_layout)

        # Create a text area for results
        results_text = QPlainTextEdit(self)
        results_text.setReadOnly(True)
        self.setup_copyable_text_area(results_text)
        layout.addWidget(results_text)

        # Add buttons
        button_layout = QHBoxLayout()

        start_button = QPushButton("Start Check", dialog)
        cancel_button = QPushButton("Cancel", dialog)
        cancel_button.setEnabled(False)
        close_button = QPushButton("Close", dialog)
        close_button.clicked.connect(lambda: self.close_window(dialog))

        button_layout.addWidget(start_button)
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

        # Show the dialog
        dialog.show()

        # Function to start the health check
        def start_health_check():
            # Clear previous results
            results_text.clear()

            # Disable start button, enable cancel button
            start_button.setEnabled(False)
            cancel_button.setEnabled(True)
            use_vpn_checkbox.setEnabled(False)
            emails_per_ip_spin.setEnabled(False)

            # Create a thread and worker for the health check
            self.health_check_thread = QThread()
            self.health_check_worker = HealthCheckWorker(
                f"{home}/{self.senders_file_name}",
                self.failed_senders_file,
                use_vpn_checkbox.isChecked(),
                emails_per_ip_spin.value()
            )

            # Connect signals
            self.health_check_worker.progress_update.connect(
                lambda msg: self.update_health_check_results(results_text, msg)
            )
            cancel_button.clicked.connect(self.health_check_worker.cancel_check)

            # Set up the thread
            self.health_check_worker.moveToThread(self.health_check_thread)
            self.health_check_thread.started.connect(self.health_check_worker.run)
            self.health_check_worker.finished.connect(self.health_check_thread.quit)
            self.health_check_worker.finished.connect(self.health_check_worker.deleteLater)
            self.health_check_thread.finished.connect(self.health_check_thread.deleteLater)
            self.health_check_thread.finished.connect(
                lambda: cancel_button.setEnabled(False)
            )
            self.health_check_thread.finished.connect(
                lambda: start_button.setEnabled(True)
            )
            self.health_check_thread.finished.connect(
                lambda: use_vpn_checkbox.setEnabled(True)
            )
            self.health_check_thread.finished.connect(
                lambda: emails_per_ip_spin.setEnabled(True)
            )

            # Start the thread
            self.health_check_thread.start()

        # Connect the start button
        start_button.clicked.connect(start_health_check)

        # Show the dialog (will block until closed)
        dialog.exec()

        # If the thread is still running when the dialog is closed, cancel it
        try:
            if hasattr(self, 'health_check_thread') and self.health_check_thread is not None:
                if self.health_check_thread.isRunning():
                    self.health_check_worker.cancel_check()
                    self.health_check_thread.quit()
                    # Wait with timeout to avoid hanging
                    if not self.health_check_thread.wait(3000):  # 3 second timeout
                        self.logger.warning("Health check thread did not terminate within timeout")
                # Clear the references
                self.health_check_thread = None
                self.health_check_worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up health check thread: {str(e)}")
            # Ensure references are cleared even if there's an error
            self.health_check_thread = None
            self.health_check_worker = None

    def update_health_check_results(self, text_widget, message):
        """Update the health check results text widget"""
        text_widget.setPlainText(text_widget.toPlainText() + message + "\n")
        # Scroll to the bottom
        text_widget.verticalScrollBar().setValue(text_widget.verticalScrollBar().maximum())

    def bounce_collector(self):
        """Collect bounce emails from sender accounts"""
        # Create a dialog to show progress and results
        dialog = QDialog(self)
        dialog.setFixedSize(900, 700)
        dialog.setWindowTitle("Bounce Email Collector")
        layout = QVBoxLayout(dialog)

        # Add options at the top
        from PyQt6.QtWidgets import QHBoxLayout, QCheckBox, QLabel, QSpinBox

        # First row of options
        options_layout1 = QHBoxLayout()

        # VPN checkbox
        use_vpn_checkbox = QCheckBox("Use VPN (HMA)", dialog)
        use_vpn_checkbox.setChecked(True)
        options_layout1.addWidget(use_vpn_checkbox)

        # Emails per IP change
        emails_per_ip_label = QLabel("Accounts per IP change:", dialog)
        options_layout1.addWidget(emails_per_ip_label)

        emails_per_ip_spin = QSpinBox(dialog)
        emails_per_ip_spin.setMinimum(1)
        emails_per_ip_spin.setMaximum(20)
        emails_per_ip_spin.setValue(5)
        options_layout1.addWidget(emails_per_ip_spin)

        # Add bounce count display
        bounce_count_label = QLabel("Total bounces found: 0", dialog)
        bounce_count_label.setStyleSheet("font-weight: bold; color: #d32f2f;")
        options_layout1.addWidget(bounce_count_label)

        # Second row of options
        options_layout2 = QHBoxLayout()

        # Clean senders checkbox
        clean_senders_checkbox = QCheckBox("Clean bounced emails from senders file", dialog)
        clean_senders_checkbox.setChecked(False)
        clean_senders_checkbox.setToolTip("Remove bounced email addresses from the senders file after collection")
        options_layout2.addWidget(clean_senders_checkbox)

        # Add options to layout
        layout.addLayout(options_layout1)
        layout.addLayout(options_layout2)

        # Create a text area for results
        results_text = QPlainTextEdit(self)
        results_text.setReadOnly(True)
        self.setup_copyable_text_area(results_text)
        layout.addWidget(results_text)

        # Add buttons
        button_layout = QHBoxLayout()

        start_button = QPushButton("Start Collection", dialog)
        cancel_button = QPushButton("Cancel", dialog)
        cancel_button.setEnabled(False)
        view_bounces_button = QPushButton("View Bounces", dialog)
        export_bounces_button = QPushButton("Export Bounces", dialog)
        close_button = QPushButton("Close", dialog)
        close_button.clicked.connect(lambda: self.close_window(dialog))

        button_layout.addWidget(start_button)
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(view_bounces_button)
        button_layout.addWidget(export_bounces_button)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

        # Show the dialog
        dialog.show()

        # Function to start the bounce collection
        def start_bounce_collection():
            # Clear previous results
            results_text.clear()
            bounce_count_label.setText("Total bounces found: 0")

            # Disable start button, enable cancel button
            start_button.setEnabled(False)
            cancel_button.setEnabled(True)
            use_vpn_checkbox.setEnabled(False)
            emails_per_ip_spin.setEnabled(False)
            clean_senders_checkbox.setEnabled(False)

            # Create a thread and worker for the bounce collection
            self.bounce_collection_thread = QThread()
            self.bounce_collection_worker = BounceCollectorWorker(
                f"{home}/{self.senders_file_name}",
                self.bounced_emails_file,
                use_vpn_checkbox.isChecked(),
                emails_per_ip_spin.value(),
                clean_senders_checkbox.isChecked()
            )

            # Connect signals
            self.bounce_collection_worker.progress_update.connect(
                lambda msg: self.update_bounce_collection_results(results_text, msg)
            )
            self.bounce_collection_worker.bounce_count_update.connect(
                lambda count: bounce_count_label.setText(f"Total bounces found: {count}")
            )
            cancel_button.clicked.connect(self.bounce_collection_worker.cancel_check)

            # Set up the thread
            self.bounce_collection_worker.moveToThread(self.bounce_collection_thread)
            self.bounce_collection_thread.started.connect(self.bounce_collection_worker.run)
            self.bounce_collection_worker.finished.connect(self.bounce_collection_thread.quit)
            self.bounce_collection_worker.finished.connect(self.bounce_collection_worker.deleteLater)
            self.bounce_collection_thread.finished.connect(self.bounce_collection_thread.deleteLater)
            self.bounce_collection_thread.finished.connect(
                lambda: cancel_button.setEnabled(False)
            )
            self.bounce_collection_thread.finished.connect(
                lambda: start_button.setEnabled(True)
            )
            self.bounce_collection_thread.finished.connect(
                lambda: use_vpn_checkbox.setEnabled(True)
            )
            self.bounce_collection_thread.finished.connect(
                lambda: emails_per_ip_spin.setEnabled(True)
            )
            self.bounce_collection_thread.finished.connect(
                lambda: clean_senders_checkbox.setEnabled(True)
            )

            # Start the thread
            self.bounce_collection_thread.start()

        # Connect the start button
        start_button.clicked.connect(start_bounce_collection)

        # Connect view bounces button
        view_bounces_button.clicked.connect(self.view_bounced_emails)

        # Connect export bounces button
        export_bounces_button.clicked.connect(self.export_bounced_emails)

        # Show the dialog (will block until closed)
        dialog.exec()

        # If the thread is still running when the dialog is closed, cancel it
        try:
            if hasattr(self, 'bounce_collection_thread') and self.bounce_collection_thread is not None:
                if self.bounce_collection_thread.isRunning():
                    self.bounce_collection_worker.cancel_check()
                    self.bounce_collection_thread.quit()
                    # Wait with timeout to avoid hanging
                    if not self.bounce_collection_thread.wait(3000):  # 3 second timeout
                        self.logger.warning("Bounce collection thread did not terminate within timeout")
                # Clear the references
                self.bounce_collection_thread = None
                self.bounce_collection_worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up bounce collection thread: {str(e)}")
            # Ensure references are cleared even if there's an error
            self.bounce_collection_thread = None
            self.bounce_collection_worker = None

    def update_bounce_collection_results(self, text_widget, message):
        """Update the bounce collection results text widget"""
        text_widget.setPlainText(text_widget.toPlainText() + message + "\n")
        # Scroll to the bottom
        text_widget.verticalScrollBar().setValue(text_widget.verticalScrollBar().maximum())

    def view_bounced_emails(self):
        """Open the bounced emails file in a dialog for viewing"""
        from PyQt6.QtWidgets import QHBoxLayout
        dialog = QDialog(self)
        dialog.setFixedSize(800, 600)
        dialog.setWindowTitle("Bounced Emails")
        layout = QVBoxLayout(dialog)
        text_editor = QPlainTextEdit(self)
        text_editor.setReadOnly(True)
        self.setup_copyable_text_area(text_editor)
        layout.addWidget(text_editor)

        # Check if the file exists, create it if it doesn't
        import os
        if not os.path.exists(self.bounced_emails_file):
            with open(self.bounced_emails_file, 'w', encoding='utf-8') as file:
                file.write("# Bounced emails log\n")
                file.write("# Format: Timestamp | Bounced Email\n\n")

        # Load the file content
        try:
            with open(self.bounced_emails_file, 'r', encoding='utf-8') as file:
                text_content = file.read()
                text_editor.setPlainText(text_content)
        except Exception as e:
            self.logger.error(f"Error while opening bounced emails file >> {str(e)}")
            text_editor.setPlainText(f"Error opening file: {str(e)}")

        # Add buttons
        button_layout = QHBoxLayout()

        export_button = QPushButton("Export List", dialog)
        export_button.clicked.connect(lambda: self.export_bounced_emails())

        clear_button = QPushButton("Clear", dialog)
        clear_button.clicked.connect(lambda: self.clear_bounced_emails_file(text_editor))

        close_button = QPushButton("Close", dialog)
        close_button.clicked.connect(lambda: self.close_window(dialog))

        button_layout.addWidget(export_button)
        button_layout.addWidget(clear_button)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        dialog.exec()

    def export_bounced_emails(self):
        """Export bounced emails as a simple list"""
        try:
            import os
            import datetime

            # Read bounced emails file
            bounced_emails = set()

            if os.path.exists(self.bounced_emails_file):
                with open(self.bounced_emails_file, 'r', encoding='utf-8') as file:
                    for line in file:
                        if line.startswith('#') or not line.strip():
                            continue
                        parts = line.split(' | ', 1)
                        if len(parts) >= 2:
                            email = parts[1].strip()
                            if '@' in email:
                                bounced_emails.add(email)

            if not bounced_emails:
                QMessageBox.information(self, "Export", "No bounced emails found to export.")
                return

            # Create export file
            export_file_path = f"{home}/bounced_emails_export.txt"
            with open(export_file_path, 'w', encoding='utf-8') as export_file:
                export_file.write("# List of bounced/non-working recipient emails\n")
                export_file.write(f"# Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                export_file.write(f"# Total emails: {len(bounced_emails)}\n\n")

                for email in sorted(bounced_emails):
                    export_file.write(f"{email}\n")

            QMessageBox.information(
                self,
                "Export Successful",
                f"Bounced emails exported to:\n{export_file_path}\n\nTotal emails exported: {len(bounced_emails)}"
            )

        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export bounced emails: {str(e)}")

    def clear_bounced_emails_file(self, text_editor):
        """Clear the bounced emails file and reset it with headers"""
        try:
            # Set the text in the editor
            text_editor.setPlainText("# Bounced emails log\n# Format: Timestamp | Bounced Email\n\n")

            # Also save to the file
            with open(self.bounced_emails_file, 'w', encoding='utf-8') as file:
                file.write("# Bounced emails log\n# Format: Timestamp | Bounced Email\n\n")

            QMessageBox.information(text_editor, "Clear File", "Bounced emails file cleared successfully.")
        except Exception as e:
            QMessageBox.critical(text_editor, "Error", f"Failed to clear file: {str(e)}")

    def load_saved_data(self):
        try:
            with open(self.saved_data_js, 'r') as file:
                data = json.load(file)
                self.subject_input.setText(data['subject'])
                self.from_input.setText(data['from'])
                self.limit_spin.setValue(data['limit'])
                self.delay_spin.setValue(data['delay'])
                self.only_groups.setChecked(data['only_groups'])
                self.isp_list.setCurrentText(data['isp'])
                if 'rotation' in data:
                    self.rotation_check.setChecked(data['rotation'])
                if 'gmx_slow_send' in data:
                    self.gmx_slow_send.setChecked(data['gmx_slow_send'])
                if 'emails_per_batch' in data:
                    self.emails_per_batch_spin.setValue(data['emails_per_batch'])
        except FileNotFoundError:
            pass



    def save_data_to_json(self):
        data = {
            'subject': self.subject_input.text(),
            'from': self.from_input.text(),
            'limit': self.limit_spin.value(),
            'delay': self.delay_spin.value(),
            'only_groups': self.only_groups.isChecked(),
            'isp': self.isp_list.currentText(),
            'rotation': self.rotation_check.isChecked(),
            'gmx_slow_send': self.gmx_slow_send.isChecked(),
            'emails_per_batch': self.emails_per_batch_spin.value(),
        }
        with open(self.saved_data_js, 'w') as file:
            json.dump(data, file)

    def closeEvent(self, event):
        """Handle application close event - clean up threads"""
        self.logger.info("Application closing - cleaning up threads")

        # Clean up health check thread if it exists
        try:
            if hasattr(self, 'health_check_thread') and self.health_check_thread is not None:
                if self.health_check_thread.isRunning():
                    self.logger.info("Stopping health check thread")
                    self.health_check_worker.cancel_check()
                    self.health_check_thread.quit()
                    # Wait with timeout to avoid hanging
                    if not self.health_check_thread.wait(2000):  # 2 second timeout
                        self.logger.warning("Health check thread did not terminate within timeout")
                self.health_check_thread = None
                self.health_check_worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up health check thread: {str(e)}")

        # Clean up bounce collection thread if it exists
        try:
            if hasattr(self, 'bounce_collection_thread') and self.bounce_collection_thread is not None:
                if self.bounce_collection_thread.isRunning():
                    self.logger.info("Stopping bounce collection thread")
                    self.bounce_collection_worker.cancel_check()
                    self.bounce_collection_thread.quit()
                    # Wait with timeout to avoid hanging
                    if not self.bounce_collection_thread.wait(2000):  # 2 second timeout
                        self.logger.warning("Bounce collection thread did not terminate within timeout")
                self.bounce_collection_thread = None
                self.bounce_collection_worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up bounce collection thread: {str(e)}")

        # Clean up email sending thread if it exists
        try:
            if hasattr(self, 'thread') and self.thread is not None:
                if self.thread.isRunning():
                    self.logger.info("Stopping email sending thread")
                    if hasattr(self, 'worker') and self.worker is not None:
                        self.worker.stop_signal.emit()
                    self.thread.quit()
                    # Wait with timeout to avoid hanging
                    if not self.thread.wait(2000):  # 2 second timeout
                        self.logger.warning("Email sending thread did not terminate within timeout")
                self.thread = None
                self.worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up email sending thread: {str(e)}")

        # Clean up export thread if it exists
        try:
            if hasattr(self, 'export_thread') and self.export_thread is not None:
                if self.export_thread.isRunning():
                    self.logger.info("Stopping export thread")
                    self.export_thread.quit()
                    # Wait with timeout to avoid hanging
                    if not self.export_thread.wait(2000):  # 2 second timeout
                        self.logger.warning("Export thread did not terminate within timeout")
                self.export_thread = None
                self.export_worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up export thread: {str(e)}")

        # Save data before closing
        try:
            self.save_data_to_json()
        except Exception as e:
            self.logger.error(f"Error saving data: {str(e)}")

        # Accept the close event
        event.accept()


def initialize_com():
    """Initialize COM for the main thread"""
    try:
        import pythoncom
        pythoncom.CoInitialize()
        print("COM initialized for main thread")
        return True
    except ImportError:
        print("pythoncom not available")
        return False
    except Exception as e:
        print(f"Failed to initialize COM library ({str(e)})")
        return False


if __name__ == "__main__":
    initialize_com()

    print("Starting>>>")

    # Check if pywinauto is installed
    try:
        import pywinauto
    except ImportError:
        # Show a message box about missing dependencies
        from PyQt6.QtWidgets import QApplication, QMessageBox
        app = QApplication(sys.argv)
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Icon.Warning)
        msg.setWindowTitle("Missing Dependencies")
        msg.setText("Some required dependencies are missing.")
        msg.setInformativeText("Would you like to run the dependency checker to install them?")
        msg.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if msg.exec() == QMessageBox.StandardButton.Yes:
            # Run the dependency checker
            try:
                import subprocess
                import os

                # Get the path to the dependency checker
                script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                checker_path = os.path.join(script_dir, "check_dependencies.py")

                # Run the checker
                subprocess.Popen([sys.executable, checker_path])
            except Exception as e:
                error_msg = QMessageBox()
                error_msg.setIcon(QMessageBox.Icon.Critical)
                error_msg.setWindowTitle("Error")
                error_msg.setText(f"Failed to run dependency checker: {str(e)}")
                error_msg.exec()

        sys.exit(1)

    # All dependencies are installed, start the application
    app = QApplication(sys.argv)
    window = MainWindow()
    #window.setFixedSize(602, 478)
    app_icon = QIcon()
    app_icon.addFile(f'{home}/img/Groups_16.png', QtCore.QSize(16, 16))
    app_icon.addFile(f'{home}/img/Groups_24.png', QtCore.QSize(24, 24))
    app_icon.addFile(f'{home}/img/Groups_32.png', QtCore.QSize(32, 32))
    app_icon.addFile(f'{home}/img/Groups_48.png', QtCore.QSize(48, 48))
    app_icon.addFile(f'{home}/img/Groups_256.png', QtCore.QSize(256, 256))
    window.setWindowIcon(app_icon)
    window.show()
    app.exec()